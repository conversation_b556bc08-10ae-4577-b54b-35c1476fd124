<!--
  资源节点音频组件
  处理音频生成和管理功能
-->
<template>
  <div class="audio-component">
    <div class="preview-section">
      <div class="preview-header">
        <div>音频：</div>
        <el-button-group>
          <el-button size="small" type="primary" @click="showDialog" :disabled="!hasSourceNode">
            <el-icon class="el-icon--left">
              <i-ep-setting />
            </el-icon>
            编辑音频
          </el-button>
        </el-button-group>
      </div>
      <div class="preview-info audio-preview"
        :class="{ 'success-bg': getAudioPercentage() === 100 && totalAudioCount > 0, 'warning-bg': getAudioPercentage() < 100 && totalAudioCount > 0 }">
        <div class="preview-progress">
          <div class="progress-bar">
            <div class="progress-fill" :style="{ width: getAudioPercentage() + '%' }"></div>
          </div>
          <div class="progress-text">
            已生成: {{ audioCount }}/{{ totalAudioCount }}
            <span v-if="getAudioPercentage() === 100" class="status-tag success">完成</span>
            <span v-else-if="totalAudioCount > 0" class="status-tag warning">未完成</span>
            <span v-else class="status-tag info">无需生成</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 音频编辑对话框 -->
    <standard-dialog v-model="dialogVisible" title="音频设置" width="80%" :before-close="handleDialogClose">
      <div class="dialog-header-fixed">
        <div class="dialog-toolbar">
          <div class="filter-section">
            <span class="label">音频类型：</span>
            <el-radio-group v-model="audioTypeFilter" size="small">
              <el-radio-button value="all">全部</el-radio-button>
              <el-radio-button value="source_language">源语言</el-radio-button>
              <el-radio-button value="translation_language">翻译语言</el-radio-button>
            </el-radio-group>
          </div>
        </div>

        <!-- 语速设置区域 -->
        <div class="speed-settings">
          <div class="speed-row">
            <span class="speed-label">{{ sourceLanguageLabel }}内容:</span>
            <el-input-number v-model="sourceSpeed" :min="0.75" :max="1.5" :step="0.05" :precision="2" size="small"
              controls-position="right" style="width: 80px;" />
            <el-dropdown v-if="primarySourceLanguage === 'ja' && hasSourceLanguageContent" trigger="click"
              @command="setSourceLanguageLevel">
              <el-button size="small" type="primary" plain>日语级别</el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item v-for="level in JAPANESE_LEVEL_RATES" :key="level.value" :command="level.value">
                    {{ level.label }}
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
            <el-button size="small" type="success" @click="applySourceSpeed">应用</el-button>

            <span class="speed-label" style="margin-left: 20px;">翻译:</span>
            <el-input-number v-model="translationSpeed" :min="0.75" :max="1.5" :step="0.05" :precision="2" size="small"
              controls-position="right" style="width: 80px;" />
            <el-button size="small" type="success" @click="applyTranslationSpeed">应用</el-button>
          </div>
        </div>
      </div>

      <div class="dialog-content-scrollable">
        <!-- 音频卡片列表 -->
        <div class="audio-cards">
          <!-- 源语言内容组 -->
          <div v-if="hasSourceContent" class="audio-group">
            <div class="group-header">
              <h3>{{ sourceLanguageLabel }}内容</h3>
            </div>
            <div class="cards-container">
              <div v-for="(item, index) in sourceItems" :key="item.id" class="audio-card">
                <div class="card-header">
                  <span class="card-index">{{ index + 1 }}</span>
                  <el-tag size="small" :type="item.audioType === 'keyword' ? 'warning' : 'primary'">
                    {{ item.audioType === 'source' ? `${sourceLanguageLabel}内容` : '关键词' }}
                  </el-tag>
                </div>
                <div class="card-content">
                  <div class="text-content">
                    <div v-if="getAnnotation(item.id) && item.language === 'ja'"
                      v-html="generateRubyHTML(item.text, getAnnotation(item.id))"></div>
                    <div v-else v-html="highlightOriginalWords(item)"></div>
                    <div v-if="item.processedText && item.specialWordProcessed" class="processed-text">
                      <div class="text-label">处理后：</div>
                      <div v-html="highlightReplacedWords(item)"></div>
                    </div>
                  </div>
                  <div class="control-panel">
                    <div class="status-tags">
                      <el-tag size="small" :type="getStatusTagType(item.status)">
                        {{ getStatusLabel(item.status) }}
                      </el-tag>
                      <el-tag v-if="item.audioSource === 'manual'" type="success" size="small">手动</el-tag>
                      <el-tag v-else-if="item.audioSource === 'tts'" type="primary" size="small">TTS</el-tag>
                      <el-tag v-if="item.specialWordProcessed" type="warning" size="small">特殊</el-tag>
                      <el-dropdown trigger="click" size="small">
                        <el-button size="small" circle>
                          <el-icon><i-ep-more-filled /></el-icon>
                        </el-button>
                        <template #dropdown>
                          <el-dropdown-menu>
                            <el-dropdown-item v-if="item.audioSource !== 'tts'" @click="switchToTTS(item)">
                              改为TTS生成
                            </el-dropdown-item>
                            <el-dropdown-item v-if="item.audioSource !== 'manual'" @click="switchToManual(item)">
                              改为手动切割
                            </el-dropdown-item>
                          </el-dropdown-menu>
                        </template>
                      </el-dropdown>
                    </div>
                    <div class="player-control">
                      <div v-if="item.url" class="audio-player">
                        <SimpleAudioPlayer :url="item.url" />
                      </div>
                      <el-button v-else size="small" type="primary" @click="generateAudio(item)"
                        :disabled="isGeneratingAudio">
                        生成音频
                      </el-button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 翻译内容组 -->
          <div v-if="hasTranslationContent" class="audio-group">
            <div class="group-header">
              <h3>翻译内容</h3>
            </div>
            <div class="cards-container">
              <div v-for="(item, index) in translationItems" :key="item.id" class="audio-card">
                <div class="card-header">
                  <span class="card-index">{{ index + 1 }}</span>
                  <el-tag size="small" type="success">翻译</el-tag>
                </div>
                <div class="card-content">
                  <div class="text-content">
                    <div v-html="highlightOriginalWords(item)"></div>
                  </div>
                  <div class="control-panel">
                    <div class="status-tags">
                      <el-tag size="small" :type="getStatusTagType(item.status)">
                        {{ getStatusLabel(item.status) }}
                      </el-tag>
                      <el-tag type="primary" size="small">TTS</el-tag>
                      <el-dropdown trigger="click" size="small">
                        <el-button size="small" circle>
                          <el-icon><i-ep-more-filled /></el-icon>
                        </el-button>
                        <template #dropdown>
                          <el-dropdown-menu>
                            <el-dropdown-item v-if="item.audioSource !== 'tts'" @click="switchToTTS(item)">
                              改为TTS生成
                            </el-dropdown-item>
                            <el-dropdown-item v-if="item.audioSource !== 'manual'" @click="switchToManual(item)">
                              改为手动切割
                            </el-dropdown-item>
                          </el-dropdown-menu>
                        </template>
                      </el-dropdown>
                    </div>
                    <div class="player-control">
                      <div v-if="item.url" class="audio-player">
                        <SimpleAudioPlayer :url="item.url" />
                      </div>
                      <el-button v-else size="small" type="primary" @click="generateAudio(item)"
                        :disabled="isGeneratingAudio">
                        生成音频
                      </el-button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <div class="dialog-footer-left">
            <!-- 左侧不再放置忽略缓存复选框 -->
          </div>
          <div class="dialog-footer-right">
            <el-button @click="handleDialogClose">关闭</el-button>
            <el-button type="primary" @click="saveAudioSettings">保存设置</el-button>
            <el-button type="warning" @click="showSpecialWordDialog">
              <el-icon style="margin-right: 0.25rem;">
                <i-ep-setting />
              </el-icon>
              特殊词汇管理
            </el-button>

            <!-- 生成音频下拉按钮 -->
            <el-dropdown type="success" :loading="isGeneratingAudio">
              <el-button type="success" :loading="isGeneratingAudio" class="generate-audio-button">
                <el-icon style="margin-right: 0.25rem;">
                  <i-ep-video-play />
                </el-icon>
                生成音频
                <el-icon style="margin-left: 0.25rem;">
                  <i-ep-arrow-down />
                </el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="generateAllAudio()">生成(未处理项)</el-dropdown-item>
                  <el-dropdown-item @click="generateFailedAudio">重新生成(失败项)</el-dropdown-item>
                  <el-dropdown-item @click="generateAllAudio(true)">重新生成(全部)</el-dropdown-item>
                  <el-dropdown-item divided @click="openAudioCuttingDialog">
                    <el-icon style="margin-right: 4px;">
                      <i-ep-upload />
                    </el-icon>
                    音频切割
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>

            <!-- 忽略缓存复选框移到最右边 -->
            <el-checkbox v-model="ignoreCache" label="忽略缓存" border style="margin-left: 0.5rem;" />
          </div>
        </div>
      </template>
    </standard-dialog>

    <!-- 特殊词汇管理对话框 -->
    <SpecialWordDialog v-model="specialWordDialogVisible" @saved="handleSpecialWordsSaved" />

    <!-- 音频切割对话框 -->
    <AudioCuttingDialog v-model="audioCuttingDialogVisible" :text-segments="targetTextSegments"
      @confirm="handleAudioCuttingConfirm" />
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue';
// 图标现在通过 unplugin-icons 自动导入，无需手动导入
import { ElMessage, ElLoading, ElMessageBox } from 'element-plus';
import StandardDialog from '../common/StandardDialog.vue';
import SpecialWordDialog from '../settings/SpecialWordDialog.vue';
import SimpleAudioPlayer from '../common/SimpleAudioPlayer.vue';
import AudioCuttingDialog from '../audio/AudioCuttingDialog.vue';
import { getLanguageLabel } from '@/config/languages';
import { JAPANESE_LEVEL_RATES, getDefaultRateForLanguage } from '@/config/speechRates';
import { getTtsInfo, getVoiceNameSync } from '@/services/ttsInfoService';
import {
  getStatusTagType,
  getStatusLabel,
  getSpeakerTagType,
  highlightOriginalWords,
  highlightReplacedWords,
  getItemSpeakerInfo,
  prepareAudioRequestItem
} from './ResourceNodeUtils';
import { processSpecialWords } from '@/services/specialWordService';
import { generateRubyHTML } from '@/utils/rubyGenerator';
import { md5 } from '@/utils/md5';
import httpClient from '@/utils/httpClient';
import { API_ENDPOINTS } from '@/config/api';

const props = defineProps({
  nodeId: {
    type: String,
    required: true
  },
  processedResult: {
    type: Object,
    default: () => null
  },
  params: {
    type: Object,
    required: true
  },
  hasSourceNode: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:params', 'process-node']);

// 对话框状态
const dialogVisible = ref(false);
const specialWordDialogVisible = ref(false);
const isGeneratingAudio = ref(false);
const ignoreCache = ref(false); // 控制是否忽略缓存

// 音频相关变量
const audioItems = ref([]); // 当前编辑中的音频项
const originalAudioItems = ref([]); // 原始音频项的备份，用于取消修改
const hasUnsavedChanges = ref(false); // 是否有未保存的修改
const audioTypeFilter = ref('all'); // 音频类型过滤器：all, source_language, translation_language

// 音频切割相关变量
const audioCuttingDialogVisible = ref(false); // 音频切割对话框显示状态
const targetTextSegments = ref([]); // 目标文本分句（动态）

// 语速设置相关变量
const sourceSpeed = ref(1.0); // 默认源语言语速
const translationSpeed = ref(1.0); // 默认翻译语速

// el-table-v2 列配置
const tableColumns = [
  {
    key: 'index',
    title: '序号',
    dataKey: 'index',
    width: 50,
    align: 'center'
  },
  {
    key: 'type',
    title: '类型/语言',
    dataKey: 'type',
    width: 120,
    align: 'center'
  },
  {
    key: 'content',
    title: '内容',
    dataKey: 'content',
    width: 300,
    flexGrow: 1
  },
  {
    key: 'speaker',
    title: '说话人/声音',
    dataKey: 'speaker',
    width: 120
  },
  {
    key: 'speed',
    title: '语速/时长',
    dataKey: 'speed',
    width: 80,
    align: 'center'
  },
  {
    key: 'player',
    title: '播放',
    dataKey: 'player',
    width: 180,
    align: 'center'
  },
  {
    key: 'status',
    title: '状态',
    dataKey: 'status',
    width: 140
  },
  {
    key: 'actions',
    title: '操作',
    dataKey: 'actions',
    width: 120,
    align: 'center'
  }
];

// 移除旧的hasJapaneseSource计算属性，已被hasSourceLanguageContent替代

// 音频相关计算属性
const audioCount = computed(() => {
  if (!props.processedResult) return 0;

  // 优先使用 sourceSegments 作为数据源，确保一致性
  if (!props.processedResult.sourceSegments || props.processedResult.sourceSegments.length === 0) {
    return 0;
  }

  if (!props.processedResult.audioItems || props.processedResult.audioItems.length === 0) {
    return 0;
  }

  // 获取所有非关键词的分句
  const validSegments = props.processedResult.sourceSegments.filter(seg =>
    !seg.isKeyword && seg.type !== 'keyword'
  );

  // 获取目标语言列表
  const targets = props.params.targets || [];

  // 调试信息
  console.log('音频计算调试信息:', {
    totalSegments: props.processedResult.sourceSegments.length,
    validSegments: validSegments.length,
    targets: targets,
    totalAudioItems: props.processedResult.audioItems.length
  });

  // 计算已生成的音频项数量
  let count = 0;

  // 计算源语言音频
  validSegments.forEach(segment => {
    const audioItem = props.processedResult.audioItems.find(item =>
      item.id === segment.id && item.audioType === 'source'
    );
    if (audioItem && audioItem.status === 'success') {
      count++;
      console.log(`已生成源音频: ${segment.id}`);
    } else {
      console.log(`未生成源音频: ${segment.id}`);
    }
  });

  // 计算翻译音频
  if (targets.length > 0 && props.params.translations) {
    validSegments.forEach(segment => {
      targets.forEach(target => {
        // 检查是否有该语言的翻译
        const hasTranslation = props.params.translations[target] &&
          props.params.translations[target][segment.id] &&
          props.params.translations[target][segment.id].trim();

        if (hasTranslation) {
          const translationId = `${segment.id}_${target}`;
          const audioItem = props.processedResult.audioItems.find(item =>
            item.id === translationId && item.audioType === 'translation'
          );
          if (audioItem && audioItem.status === 'success') {
            count++;
            console.log(`已生成翻译音频: ${translationId}`);
          } else {
            console.log(`未生成翻译音频: ${translationId}`);
          }
        }
      });
    });
  }

  console.log(`音频统计: ${count} 个已生成`);
  return count;
});

const totalAudioCount = computed(() => {
  if (!props.processedResult) return 0;

  // 优先使用 sourceSegments 作为数据源，确保一致性
  if (!props.processedResult.sourceSegments || props.processedResult.sourceSegments.length === 0) {
    return 0;
  }

  // 获取所有非关键词的分句
  const validSegments = props.processedResult.sourceSegments.filter(seg =>
    !seg.isKeyword && seg.type !== 'keyword'
  );

  // 获取目标语言列表
  const targets = props.params.targets || [];

  // 计算总音频数量
  let total = validSegments.length; // 源语言音频

  // 计算翻译音频数量
  if (targets.length > 0 && props.params.translations) {
    validSegments.forEach(segment => {
      targets.forEach(target => {
        // 检查是否有该语言的翻译
        const hasTranslation = props.params.translations[target] &&
          props.params.translations[target][segment.id] &&
          props.params.translations[target][segment.id].trim();

        if (hasTranslation) {
          total++;
        }
      });
    });
  }

  console.log(`音频总数: ${total} 个需要生成`);
  return total;
});

// 过滤音频项
const filteredAudioItems = computed(() => {
  if (audioTypeFilter.value === 'all') return audioItems.value;

  if (audioTypeFilter.value === 'source_language') {
    // 源语言包括原文内容和关键词
    return audioItems.value.filter(item =>
      item.audioType === 'source' ||
      item.audioType === 'keyword' ||
      item.type === 'keyword' ||
      item.isKeyword
    );
  } else if (audioTypeFilter.value === 'translation_language') {
    // 翻译语言只包括翻译内容
    return audioItems.value.filter(item => item.audioType === 'translation');
  }

  return audioItems.value;
});

// 过滤当前tab对应的文本分句
const filteredTextSegments = computed(() => {
  if (audioTypeFilter.value === 'all') return allTextSegments.value;

  if (audioTypeFilter.value === 'source_language') {
    // 源语言包括原文内容和关键词
    return allTextSegments.value.filter(segment =>
      segment.segmentType === 'source' ||
      segment.type === 'keyword' ||
      segment.isKeyword
    );
  } else if (audioTypeFilter.value === 'translation_language') {
    // 翻译语言只包括翻译内容
    return allTextSegments.value.filter(segment => segment.segmentType === 'translation');
  }

  return allTextSegments.value;
});

// 获取所有可用的文本分句（用于音频切割）
const allTextSegments = computed(() => {
  const segments = [];

  // 添加源语言分句
  if (props.processedResult && props.processedResult.sourceSegments) {
    props.processedResult.sourceSegments.forEach(segment => {
      if (!segment.isKeyword && segment.type !== 'keyword') {
        segments.push({
          ...segment,
          segmentType: 'source'
        });
      }
    });
  }

  // 添加翻译分句
  if (props.params.targets && props.params.translations) {
    props.params.targets.forEach(targetLang => {
      if (props.params.translations[targetLang]) {
        Object.keys(props.params.translations[targetLang]).forEach(segmentId => {
          const translationText = props.params.translations[targetLang][segmentId];
          if (translationText && translationText.trim()) {
            // 查找对应的源分句以继承说话人信息
            const sourceSegment = props.processedResult?.sourceSegments?.find(seg => seg.id === segmentId);
            segments.push({
              id: `${segmentId}_${targetLang}`,
              content: translationText,
              language: targetLang,
              speaker: sourceSegment?.speaker || null, // 继承源分句的说话人信息
              speed: sourceSegment?.speed || 1.0, // 继承源分句的语速信息
              segmentType: 'translation',
              sourceId: segmentId
            });
          }
        });
      }
    });
  }

  return segments;
});

// 获取标注信息
function getAnnotation(id) {
  if (!props.params.annotations) return null;
  return props.params.annotations[id] || null;
}

// 获取声音显示名称（同步版本）
function getVoiceDisplayName(voiceDbId) {
  return getVoiceNameSync(voiceDbId);
}

// 获取音频生成完成百分比
function getAudioPercentage() {
  if (!props.processedResult || !props.processedResult.audioItems || totalAudioCount.value === 0) {
    return 0;
  }
  return Math.round((audioCount.value / totalAudioCount.value) * 100);
}

// 显示对话框
async function showDialog() {
  // 并行加载TTS信息和准备音频项
  await Promise.all([
    getTtsInfo(), // 确保TTS信息已加载到缓存中
    prepareAudioItems()
  ]);

  // 初始化语速设置
  initializeSpeedSettings();

  // 显示对话框
  dialogVisible.value = true;

  // 检测所有音频项的内容变化
  await checkAllAudioItems();

  // 强制更新UI
  audioItems.value = [...audioItems.value];
}

// 处理对话框关闭
async function handleDialogClose() {
  // 如果有未保存的更改，提示用户
  if (hasUnsavedChanges.value) {
    try {
      await ElMessageBox.confirm(
        '您有未保存的更改，确定要关闭吗？未保存的更改将会丢失。',
        '关闭确认',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      );

      // 用户确认关闭，恢复原始数据
      resetToOriginal();
      dialogVisible.value = false;
    } catch (error) {
      // 用户取消关闭，不做任何操作
      console.log('用户取消关闭对话框');
    }
  } else {
    // 没有未保存的更改，直接关闭
    dialogVisible.value = false;
  }
}

// 恢复到原始数据
function resetToOriginal() {
  // 恢复原始音频项
  audioItems.value = originalAudioItems.value.map(item => ({ ...item }));
  hasUnsavedChanges.value = false;
}

// 初始化语速设置
function initializeSpeedSettings() {
  const sourceLanguage = primarySourceLanguage.value;
  const defaultSpeed = getDefaultRateForLanguage(sourceLanguage);

  // 如果没有音频项，使用语言默认值
  if (audioItems.value.length === 0) {
    sourceSpeed.value = defaultSpeed;
    translationSpeed.value = 1.0; // 默认翻译语速
    return;
  }

  // 查找原文和翻译音频项的平均语速
  let sourceSpeedSum = 0;
  let sourceCount = 0;
  let translationSpeedSum = 0;
  let translationCount = 0;

  audioItems.value.forEach(item => {
    if (item.audioType === 'source' || item.audioType === 'keyword') {
      sourceSpeedSum += item.speed || defaultSpeed;
      sourceCount++;
    } else if (item.audioType === 'translation') {
      translationSpeedSum += item.speed || 1.0;
      translationCount++;
    }
  });

  // 设置语速值
  if (sourceCount > 0) {
    sourceSpeed.value = parseFloat((sourceSpeedSum / sourceCount).toFixed(1));
  } else {
    sourceSpeed.value = defaultSpeed;
  }

  if (translationCount > 0) {
    translationSpeed.value = parseFloat((translationSpeedSum / translationCount).toFixed(1));
  }
}

// 准备音频项
async function prepareAudioItems() {
  audioItems.value = [];
  originalAudioItems.value = [];
  hasUnsavedChanges.value = false;

  // 如果没有处理结果，但有参数中的音频数据，直接使用参数中的音频数据
  if (!props.processedResult) {
    if (props.params.audioItems && props.params.audioItems.length > 0) {
      // 创建深拷贝，避免直接修改原始数据
      audioItems.value = props.params.audioItems.map(item => ({ ...item }));
      // 检测所有音频项的内容变化
      await checkAllAudioItems();
      // 保存原始数据的副本
      originalAudioItems.value = [...audioItems.value.map(item => ({ ...item }))];
      return;
    }
    return;
  }

  // 如果有音频项，直接使用处理器生成的音频项
  if (props.processedResult.audioItems && props.processedResult.audioItems.length > 0) {
    // 创建深拷贝，避免直接修改原始数据
    audioItems.value = props.processedResult.audioItems.map(item => ({ ...item }));
    // 保存原始数据的副本
    originalAudioItems.value = props.processedResult.audioItems.map(item => ({ ...item }));
  }
  // 如果没有音频项但有源文本，创建默认音频项
  else if (props.processedResult.sourceSegments && props.processedResult.sourceSegments.length > 0) {
    // 检查参数中是否有音频项
    if (props.params.audioItems && props.params.audioItems.length > 0) {
      // 创建深拷贝，避免直接修改原始数据
      audioItems.value = props.params.audioItems.map(item => ({ ...item }));
      // 保存原始数据的副本
      originalAudioItems.value = props.params.audioItems.map(item => ({ ...item }));
      return;
    }

    // 记录已处理的段落ID，避免重复添加
    const processedIds = new Set();

    props.processedResult.sourceSegments.forEach((segment) => {
      // 检查是否已处理过该段落
      if (processedIds.has(segment.id)) {
        return;
      }

      // 标记为已处理
      processedIds.add(segment.id);

      // 添加原文音频项 - 使用语言对应的默认语速
      const language = segment.language || 'ja';
      const defaultSpeed = getDefaultRateForLanguage(language);

      // 判断是否为关键词
      const isKeyword = segment.isKeyword || segment.type === 'keyword';

      audioItems.value.push({
        id: segment.id,
        text: segment.content,
        language: language,
        speaker: segment.speaker || null, // 说话人标记
        voice_db_id: segment.voice_db_id || null, // 从分句中获取voice_db_id
        url: "",
        duration: 0,
        status: "pending",
        speed: defaultSpeed, // 使用语言对应的默认语速
        sequenceId: `source_${segment.id}`,
        type: isKeyword ? "keyword" : (segment.type || "normal"), // 确保关键词类型正确
        audioType: isKeyword ? "keyword" : "source", // 关键词使用keyword类型，其他使用source
        audioSource: "tts", // 默认使用TTS生成
        isKeyword: isKeyword // 标记是否为关键词
      });

      // 为每个翻译目标语言创建音频项
      if (props.params.targets && props.params.targets.length > 0 &&
        props.params.translations && Object.keys(props.params.translations).length > 0) {

        props.params.targets.forEach(targetLang => {
          // 检查是否有该语言的翻译
          if (props.params.translations[targetLang] &&
            props.params.translations[targetLang][segment.id] &&
            props.params.translations[targetLang][segment.id].trim()) {

            // 添加翻译音频项 - 使用语言对应的默认语速
            const translationSpeed = getDefaultRateForLanguage(targetLang);

            audioItems.value.push({
              id: `${segment.id}_${targetLang}`,
              text: props.params.translations[targetLang][segment.id],
              language: targetLang,
              speaker: segment.speaker || 'default', // 继承源分句的说话人标记，默认为default
              voice_db_id: segment.voice_db_id || null, // 从分句中获取voice_db_id
              url: "",
              duration: 0,
              status: "pending",
              speed: translationSpeed, // 使用语言对应的默认语速
              sequenceId: `translation_${segment.id}_${targetLang}`,
              type: "normal", // 翻译音频使用普通文本类型
              audioType: "translation", // 标记为翻译音频
              audioSource: "tts", // 翻译音频只支持TTS生成
              sourceId: segment.id, // 关联到原文ID
              targetLanguage: targetLang // 目标语言
            });
          }
        });
      }
    });

    // 检测所有音频项的内容变化
    await checkAllAudioItems();

    // 保存原始数据的副本
    originalAudioItems.value = [...audioItems.value.map(item => ({ ...item }))];
  }
  // 如果没有处理结果中的音频项和源文本，但有参数中的音频项，使用参数中的音频项
  else if (props.params.audioItems && props.params.audioItems.length > 0) {
    // 创建深拷贝，避免直接修改原始数据
    audioItems.value = props.params.audioItems.map(item => ({ ...item }));
    // 保存原始数据的副本
    originalAudioItems.value = props.params.audioItems.map(item => ({ ...item }));
  }
}

/**
 * 计算音频生成哈希（与后端缓存键逻辑一致）
 * @param {string} processedText - 处理后的文本
 * @param {string} language - 语言
 * @param {number} speed - 语速
 * @param {number} voiceDbId - 声音数据库ID
 * @returns {string} 音频生成哈希
 */
function calculateAudioGenerationHash(processedText, language, speed, voiceDbId) {
  const data = `${processedText}|${language}|${speed}|${voiceDbId}`;
  return md5(data);
}

/**
 * 处理音频项文本并计算音频生成哈希
 * @param {Object} item - 音频项
 * @returns {Promise<Object>} 包含音频生成哈希、处理结果和说话人信息
 */
async function processAudioItemText(item) {
  try {
    // 获取标注数据
    const annotation = props.params.annotations ? props.params.annotations[item.id] : null;

    // 获取说话人信息
    const speakerInfo = await getItemSpeakerInfo(item);
    const serviceId = speakerInfo?.service_id || 'google';

    // 根据需要调用特殊词汇处理（保持processSpecialWords功能单一）
    const processResult = await processSpecialWords(
      item.text,
      annotation,
      serviceId,
      item.language
    );

    // 计算音频生成哈希（只包含影响最终音频的参数）
    const audioGenerationHash = calculateAudioGenerationHash(
      processResult.text,
      item.language,
      item.speed,
      speakerInfo?.id
    );

    // 返回音频生成哈希和处理结果
    return {
      audioGenerationHash,
      processResult,
      speakerInfo
    };
  } catch (error) {
    console.error('处理音频项文本失败:', error);
    return {
      audioGenerationHash: md5(item.text), // 降级为仅使用原文
      processResult: { isProcessed: false, text: item.text, replacements: [] },
      speakerInfo: null
    };
  }
}

/**
 * 检测所有音频项的音频生成参数变化
 */
async function checkAllAudioItems() {
  if (audioItems.value.length === 0) {
    return;
  }

  // 显示加载提示
  const loadingInstance = ElLoading.service({
    text: '正在检测音频参数变化...',
    background: 'rgba(0, 0, 0, 0.7)'
  });

  try {
    // 创建一个新的数组，避免直接修改原数组
    const updatedItems = [...audioItems.value];

    // 并行处理所有音频项
    const batchSize = 5; // 每批处理的项数
    const items = [...audioItems.value];

    for (let i = 0; i < items.length; i += batchSize) {
      const batch = items.slice(i, i + batchSize);

      // 并行处理一批音频项
      await Promise.all(batch.map(async (item) => {
        try {
          // 处理音频项文本并计算哈希
          const { audioGenerationHash, processResult } = await processAudioItemText(item);

          // 更新音频项
          const index = updatedItems.findIndex(ai => ai.id === item.id);
          if (index !== -1) {
            // 保存当前的音频生成哈希
            const currentAudioGenerationHash = updatedItems[index].audioGenerationHash;

            // 检查音频生成参数是否变化（只有TTS音频才检查参数变化）
            if (currentAudioGenerationHash &&
              currentAudioGenerationHash !== audioGenerationHash &&
              updatedItems[index].audioSource !== 'manual') {
              console.log(`[音频检测] 检测到TTS音频参数变化，重置音频状态: ${item.id}`);
              // TTS音频参数变化，重置音频状态
              updatedItems[index] = {
                ...updatedItems[index],
                url: '',
                duration: 0,
                status: 'pending',
                audioGenerationHash, // 更新音频生成哈希
                specialWordProcessed: processResult.isProcessed,
                processedText: processResult.text,
                replacements: processResult.replacements || []
              };
            } else {
              // 音频参数没有变化、首次处理或手动切割音频，只更新显示相关信息
              updatedItems[index] = {
                ...updatedItems[index],
                audioGenerationHash, // 确保哈希存在
                specialWordProcessed: processResult.isProcessed,
                processedText: processResult.text,
                replacements: processResult.replacements || []
              };
            }
          }
        } catch (error) {
          console.error('[音频检测] 处理音频项失败:', error);
        }
      }));
    }

    // 一次性更新整个数组，避免多次触发响应式更新
    audioItems.value = updatedItems;
  } finally {
    // 关闭加载提示
    loadingInstance.close();
  }
}

// 设置源语言级别对应的语速（仅日语支持级别选择）
function setSourceLanguageLevel(speed) {
  // 更新源语言语速设置
  sourceSpeed.value = speed;
}

// 应用源语言语速设置到所有源语言项目
function applySourceSpeed() {
  if (audioItems.value.length === 0) return;

  const targetLanguage = primarySourceLanguage.value;
  const languageLabel = sourceLanguageLabel.value;
  let updatedCount = 0;

  audioItems.value.forEach(item => {
    // 通用判断：只要是源语言且不是手动切割就应用
    if (item.language === targetLanguage && item.audioSource !== 'manual') {
      // 如果语速不同，才需要更新
      if (item.speed !== sourceSpeed.value) {
        // 更新语速
        item.speed = sourceSpeed.value;

        // 如果已经生成过音频，将状态重置为待生成
        if (item.status === 'success') {
          item.status = 'pending';
          item.url = '';
          item.duration = 0;
          // 清除音频生成哈希，强制重新计算
          item.audioGenerationHash = null;
        }

        updatedCount++;
        // 标记有未保存的更改
        hasUnsavedChanges.value = true;
      }
    }
  });

  // 显示提示
  const manualCount = audioItems.value.filter(item => item.language === targetLanguage && item.audioSource === 'manual').length;
  if (updatedCount > 0) {
    const message = `已将语速 ${sourceSpeed.value}x 应用到 ${updatedCount} 个${languageLabel}项目，请点击"保存设置"按钮保存更改`;
    const skipMessage = manualCount > 0 ? `（已跳过 ${manualCount} 个手动切割音频）` : '';
    ElMessage.success(message + skipMessage);
  } else {
    const message = manualCount > 0 ? `没有需要更新的${languageLabel}项目（已跳过 ${manualCount} 个手动切割音频）` : `没有需要更新的${languageLabel}项目`;
    ElMessage.info(message);
  }
}

// 应用翻译语速设置到所有翻译音频项
function applyTranslationSpeed() {
  if (audioItems.value.length === 0) return;

  // 更新所有翻译音频项的语速
  let updatedCount = 0;

  audioItems.value.forEach(item => {
    if (item.audioType === 'translation' && item.audioSource !== 'manual') {
      // 如果语速不同，才需要更新
      if (item.speed !== translationSpeed.value) {
        // 更新语速
        item.speed = translationSpeed.value;

        // 如果已经生成过音频，将状态重置为待生成
        // 注意：这里直接重置是合理的，因为语速变化必然导致音频重新生成
        if (item.status === 'success') {
          item.status = 'pending';
          item.url = '';
          item.duration = 0;
          // 清除音频生成哈希，强制重新计算
          item.audioGenerationHash = null;
        }

        updatedCount++;
        // 标记有未保存的更改
        hasUnsavedChanges.value = true;
      }
    }
  });

  // 显示提示
  const manualCount = audioItems.value.filter(item => item.audioType === 'translation' && item.audioSource === 'manual').length;
  if (updatedCount > 0) {
    const message = `已将语速 ${translationSpeed.value}x 应用到 ${updatedCount} 个翻译音频项，请点击"保存设置"按钮保存更改`;
    const skipMessage = manualCount > 0 ? `（已跳过 ${manualCount} 个手动切割音频）` : '';
    ElMessage.success(message + skipMessage);
  } else {
    const message = manualCount > 0 ? `没有需要更新的翻译音频项（已跳过 ${manualCount} 个手动切割音频）` : '没有需要更新的翻译音频项';
    ElMessage.info(message);
  }
}

// 保存音频设置
function saveAudioSettings() {
  if (audioItems.value.length === 0) {
    dialogVisible.value = false;
    return;
  }

  // 更新音频项
  const updatedParams = { ...props.params, audioItems: audioItems.value };
  emit('update:params', updatedParams);

  // 重新处理节点
  emit('process-node');

  // 更新原始数据副本，以便下次编辑时使用
  originalAudioItems.value = audioItems.value.map(item => ({ ...item }));

  // 清除未保存更改标记
  hasUnsavedChanges.value = false;

  // 关闭对话框
  dialogVisible.value = false;

  ElMessage.success('音频设置已保存');
}

// 临时保存音频设置但不关闭对话框（仅用于生成音频时）
function saveAudioSettingsWithoutClosing() {
  if (audioItems.value.length === 0) {
    return;
  }

  // 更新音频项
  const updatedParams = { ...props.params, audioItems: audioItems.value };
  emit('update:params', updatedParams);

  // 重新处理节点
  emit('process-node');

  // 更新原始数据副本，以便下次编辑时使用
  originalAudioItems.value = audioItems.value.map(item => ({ ...item }));

  // 清除未保存更改标记
  hasUnsavedChanges.value = false;
}

// 显示特殊词汇管理对话框
function showSpecialWordDialog() {
  specialWordDialogVisible.value = true;
}

// 处理特殊词汇保存
async function handleSpecialWordsSaved(options = {}) {
  try {
    // 重新处理所有音频项的文本
    if (audioItems.value.length > 0) {
      // 显示加载提示
      const loadingInstance = ElLoading.service({
        text: '正在更新音频参数...',
        background: 'rgba(0, 0, 0, 0.7)'
      });

      try {
        // 使用统一的音频参数检测函数
        await checkAllAudioItems();

        // 保存更新后的音频项
        saveAudioSettingsWithoutClosing();
      } finally {
        // 关闭加载提示
        loadingInstance.close();
      }
    }

    // 只有在非静默模式下才显示消息
    if (!options.silent) {
      ElMessage.success('特殊词汇设置已保存并应用');
    }
  } catch (error) {
    console.error('应用特殊词汇设置失败:', error);
    ElMessage.error('应用特殊词汇设置失败: ' + error.message);
  }
}

// 生成单个音频
async function generateAudio(item) {
  if (!item) {
    return;
  }

  // 检查音频来源，手动切割的音频不能通过TTS生成
  if (item.audioSource === 'manual') {
    ElMessage.warning('此音频为手动切割，请使用音频切割功能重新生成');
    return;
  }

  try {
    // 更新状态
    item.status = 'generating';
    item.error = '';

    // 处理音频项文本并计算哈希
    const { audioGenerationHash, processResult, speakerInfo } = await processAudioItemText(item);

    // 更新音频生成哈希
    item.audioGenerationHash = audioGenerationHash;

    // 如果没有找到声音ID，显示错误并返回
    if (!speakerInfo || !speakerInfo.id) {
      ElMessage.error(`无法为说话人 "${item.speaker || '默认'}" 和语言 "${item.language || 'auto'}" 找到声音ID`);
      item.status = 'error';
      item.error = '无法找到声音ID';
      return;
    }

    // 确定是否忽略缓存 - 只使用全局设置
    const shouldIgnoreCache = ignoreCache.value;

    // 获取最终文本
    const finalText = processResult.text;

    // 更新处理结果
    item.specialWordProcessed = processResult.isProcessed;
    item.processedText = processResult.text;
    item.replacements = processResult.replacements || [];

    // 准备请求项
    const requestItem = prepareAudioRequestItem(item, speakerInfo, finalText);

    // 调用音频生成API
    const result = await httpClient.post(API_ENDPOINTS.AUDIO.TTS_BATCH, {
      items: [requestItem],
      ignoreCache: shouldIgnoreCache // 使用基于项状态的ignoreCache值
    });

    if (result.success && result.results && result.results.length > 0) {
      const resultItem = result.results[0];

      // 更新结果
      item.url = resultItem.audioUrl || '';
      item.duration = resultItem.duration || 0;
      item.status = resultItem.success ? 'success' : 'error';
      item.error = resultItem.error || '';
      item.voice_db_id = speakerInfo.id;

      if (resultItem.success) {
        // 保存音频设置但不关闭对话框，确保duration值被保存
        saveAudioSettingsWithoutClosing();
        // 显示是否使用了缓存的消息
        const cacheMessage = shouldIgnoreCache ? (item.url ? '（已重新生成）' : '（已忽略缓存）') : '';
        ElMessage.success(`音频生成成功${cacheMessage}，时长${resultItem.duration.toFixed(1)}秒`);
      } else {
        throw new Error(resultItem.error || '音频生成失败');
      }
    } else {
      throw new Error(result.error || '音频生成失败');
    }
  } catch (error) {
    console.error('生成音频失败:', error);
    ElMessage.error('生成音频失败: ' + error.message);
    item.status = 'error';
    item.error = error.message;
  }
}

// 生成所有音频
async function generateAllAudio(forceRegenerate = false, customLoadingText = null) {
  if (audioItems.value.length === 0) {
    ElMessage.warning('没有可生成的音频项');
    return;
  }

  try {
    isGeneratingAudio.value = true;

    // 显示加载提示
    const tabName = audioTypeFilter.value === 'source_language' ? '源语言' :
      audioTypeFilter.value === 'translation_language' ? '翻译语言' : '当前';
    const loadingText = customLoadingText
      ? `正在${customLoadingText}...`
      : forceRegenerate ? `正在重新生成(${tabName}tab全部)...` : `正在生成(${tabName}tab未处理项)...`;
    const loadingInstance = ElLoading.service({
      text: loadingText,
      background: 'rgba(0, 0, 0, 0.7)'
    });

    // 如果强制重新生成，将当前tab下的音频项状态重置为待生成
    if (forceRegenerate) {
      filteredAudioItems.value.forEach(item => {
        item.status = 'pending';
        item.url = '';
        item.duration = 0;
      });
    }

    // 筛选出需要生成的音频项（只处理当前tab下的项目）
    let itemsToGenerate;

    // 默认只生成待处理项（状态为pending或null的项，且不是手动切割）
    if (!forceRegenerate) {
      itemsToGenerate = filteredAudioItems.value.filter(item =>
        (item.status === 'pending' || item.status === null || item.status === undefined) &&
        item.audioSource !== 'manual'
      );
    }
    // 如果是强制重新生成，则生成当前tab下所有非手动切割项
    else {
      itemsToGenerate = filteredAudioItems.value.filter(item => item.audioSource !== 'manual');
    }

    if (itemsToGenerate.length === 0) {
      loadingInstance.close();
      const message = forceRegenerate ? `${tabName}tab中所有音频已存在` : `${tabName}tab中(未处理项)已全部生成`;
      ElMessage.info(message);
      isGeneratingAudio.value = false;
      return;
    }

    // 准备所有请求项
    const allRequestItems = [];
    const failedItems = [];

    for (const item of itemsToGenerate) {
      try {
        // 获取说话人信息
        const speakerInfo = await getItemSpeakerInfo(item);

        // 如果没有找到声音ID，跳过该项并显示错误
        if (!speakerInfo || !speakerInfo.id) {
          ElMessage.error(`无法为说话人 "${item.speaker || '默认'}" 和语言 "${item.language || 'auto'}" 找到声音ID`);
          failedItems.push(item.id);
          continue;
        }

        // 处理音频项文本
        const { audioGenerationHash, processResult } = await processAudioItemText(item);

        const finalText = processResult.text;

        // 更新音频项的声音名称和音频生成哈希
        const audioItemsToUpdate = audioItems.value.filter(ai => ai.id === item.id);
        audioItemsToUpdate.forEach(audioItem => {
          if (audioItem) {
            // 更新声音名称
            if (speakerInfo.displayName) {
              audioItem.voiceName = speakerInfo.displayName;
            }
            // 更新音频生成哈希
            audioItem.audioGenerationHash = audioGenerationHash;
          }
        });

        // 准备请求项
        allRequestItems.push({
          requestItem: prepareAudioRequestItem(item, speakerInfo, finalText),
          speakerInfo
        });
      } catch (error) {
        console.error('获取说话人信息失败:', error);
        ElMessage.error(`获取说话人信息失败: ${error.message}`);
        failedItems.push(item.id);
      }
    }

    // 如果没有有效的请求项，显示错误并返回
    if (allRequestItems.length === 0) {
      loadingInstance.close();
      ElMessage.error('没有有效的音频生成请求项');
      isGeneratingAudio.value = false;
      return;
    }

    // 分批处理，每批最多10个
    const BATCH_SIZE = 10;
    const batches = [];

    for (let i = 0; i < allRequestItems.length; i += BATCH_SIZE) {
      batches.push(allRequestItems.slice(i, i + BATCH_SIZE));
    }

    // 更新加载提示
    const baseText = customLoadingText
      ? `正在${customLoadingText}`
      : forceRegenerate ? `正在重新生成(${tabName}tab全部)` : `正在生成(${tabName}tab未处理项)`;
    loadingInstance.setText(`${baseText}... (0/${batches.length}批)`);

    // 处理每一批
    let successCount = 0;
    let failureCount = 0;

    for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
      const batch = batches[batchIndex];
      const batchRequestItems = batch.map(item => item.requestItem);

      // 更新加载提示
      loadingInstance.setText(`${baseText}... (${batchIndex + 1}/${batches.length}批)`);

      try {
        // 调用音频生成API
        const result = await httpClient.post(API_ENDPOINTS.AUDIO.TTS_BATCH, {
          items: batchRequestItems,
          ignoreCache: ignoreCache.value // 添加忽略缓存参数
        });

        if (result.success && result.results) {
          // 更新音频项
          result.results.forEach(resultItem => {
            const items = audioItems.value.filter(i => i.id === resultItem.id);
            items.forEach(item => {
              // 找到对应的请求项和声音信息
              const batchItem = batch.find(bi => bi.requestItem.id === item.id);
              const voiceDbId = batchItem ? batchItem.requestItem.voice_db_id : null;

              // 更新结果
              item.url = resultItem.audioUrl || '';
              item.duration = resultItem.duration || 0;
              item.status = resultItem.success ? 'success' : 'error';
              item.error = resultItem.error || '';
              item.voice_db_id = voiceDbId;

              if (resultItem.success) {
                successCount++;
              } else {
                failureCount++;
                failedItems.push(item.id);
              }
            });
          });

          // 每批处理完成后保存一次设置
          saveAudioSettingsWithoutClosing();
        } else {
          // 批处理失败，记录失败项
          batchRequestItems.forEach(requestItem => {
            failedItems.push(requestItem.id);
            failureCount += batch.length;

            // 更新失败状态
            const items = audioItems.value.filter(i => i.id === requestItem.id);
            items.forEach(item => {
              item.status = 'error';
              item.error = result.error || '批量处理失败';
            });
          });
        }
      } catch (error) {
        console.error(`第${batchIndex + 1}批处理出错:`, error);

        // 批处理出错，记录失败项
        batchRequestItems.forEach(requestItem => {
          failedItems.push(requestItem.id);
          failureCount += batch.length;

          // 更新失败状态
          const items = audioItems.value.filter(i => i.id === requestItem.id);
          items.forEach(item => {
            item.status = 'error';
            item.error = error.message || '处理出错';
          });
        });
      }

      // 批次之间稍微暂停一下，避免服务器过载
      if (batchIndex < batches.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    // 保存最终结果
    saveAudioSettingsWithoutClosing();

    // 显示结果消息
    // 添加缓存状态信息
    const cacheMessage = ignoreCache.value ? '（已忽略缓存）' : '';
    const tabPrefix = audioTypeFilter.value === 'all' ? '' : `${tabName}tab中`;

    if (failureCount === 0) {
      ElMessage.success(`${tabPrefix}所有${successCount}个音频生成成功${cacheMessage}`);
    } else if (successCount === 0) {
      ElMessage.error(`${tabPrefix}所有${failureCount}个音频生成失败${cacheMessage}`);
    } else {
      ElMessage.warning(`${tabPrefix}音频生成部分完成${cacheMessage}: ${successCount}个成功, ${failureCount}个失败`);
    }
  } catch (error) {
    console.error('生成音频失败:', error);
    ElMessage.error('生成音频失败: ' + error.message);
  } finally {
    // 关闭加载提示
    ElLoading.service().close();
    // 确保重置生成状态
    isGeneratingAudio.value = false;
  }
}

// 重新生成失败项
async function generateFailedAudio() {
  // 筛选出当前tab下所有生成失败的音频项
  const failedItems = filteredAudioItems.value.filter(item => item.status === 'error');

  if (failedItems.length === 0) {
    const tabName = audioTypeFilter.value === 'source_language' ? '源语言' :
      audioTypeFilter.value === 'translation_language' ? '翻译语言' : '当前';
    ElMessage.warning(`${tabName}tab中没有(失败项)需要重新生成`);
    return;
  }

  // 重置状态
  failedItems.forEach(item => {
    item.status = 'pending';
    item.error = '';
  });

  // 生成音频（标记为重新生成失败项）
  await generateAllAudio(false, '重新生成(失败项)');
}

// 音频切割相关方法

function openAudioCuttingDialog() {
  if (allTextSegments.value.length === 0) {
    ElMessage.warning('没有可用的文本分句，请先连接文本内容节点');
    return;
  }

  // 批量模式：显示当前tab对应的文本分句
  targetTextSegments.value = filteredTextSegments.value;

  if (targetTextSegments.value.length === 0) {
    const tabName = audioTypeFilter.value === 'source_language' ? '源语言' :
      audioTypeFilter.value === 'translation_language' ? '翻译语言' : '当前';
    ElMessage.warning(`${tabName}tab中没有可用的文本分句`);
    return;
  }

  console.log('打开音频切割对话框（批量模式），当前tab分句数量:', targetTextSegments.value.length);
  audioCuttingDialogVisible.value = true;
}

// 重新切割单个音频项
function reopenAudioCutting(audioItem) {
  if (allTextSegments.value.length === 0) {
    ElMessage.warning('没有可用的文本分句，请先连接文本内容节点');
    return;
  }

  // 单个模式：只显示该音频项对应的文本分句
  const textSegment = allTextSegments.value.find(segment => segment.id === audioItem.id);
  if (!textSegment) {
    ElMessage.warning('找不到对应的文本分句');
    return;
  }

  targetTextSegments.value = [textSegment];
  console.log('打开音频切割对话框（单个模式），目标分句:', textSegment.content);
  audioCuttingDialogVisible.value = true;
}

async function handleAudioCuttingConfirm(result) {
  console.log('音频切割确认，结果:', result);

  try {
    // 处理音频切割结果 - 新的数据结构只有 audioSegments
    const { audioSegments } = result;

    // 更新音频项，添加手动切割的音频（音频已经上传并保存到数据库）
    audioSegments.forEach(audioSegment => {
      // 通过 textSegmentId 找到对应的文本段落
      const textSegment = allTextSegments.value.find(segment =>
        segment.id === audioSegment.textSegmentId
      );

      if (textSegment && audioSegment && audioSegment.url) {
        // 查找或创建对应的音频项
        let audioItem = audioItems.value.find(item =>
          item.id === textSegment.id
        );

        if (audioItem) {
          // 更新现有音频项，保留原有的说话人信息
          audioItem.url = audioSegment.url;
          audioItem.duration = audioSegment.duration || 0;
          audioItem.audioSource = audioSegment.audioSource || 'manual';
          audioItem.status = 'success';
        } else {
          // 创建新的音频项 - 使用文本分句的说话人信息
          const audioType = textSegment.segmentType === 'source' ? 'source' : 'translation';
          const newAudioItem = {
            id: textSegment.id,
            text: textSegment.content,
            language: textSegment.language,
            speaker: textSegment.speaker || 'default', // 使用文本分句的说话人信息
            voice_db_id: textSegment.voice_db_id || null, // 从文本分句中获取voice_db_id
            url: audioSegment.url,
            duration: audioSegment.duration || 0,
            status: 'success',
            speed: textSegment.speed || 1.0, // 使用文本分句的语速信息
            sequenceId: audioType === 'source' ? `source_${textSegment.id}` : `translation_${textSegment.id}`,
            type: textSegment.type || 'normal',
            audioType: audioType,
            audioSource: audioSegment.audioSource || 'manual'
          };

          audioItems.value.push(newAudioItem);
          audioItem = newAudioItem;
        }
      }
    });

    // 标记有未保存的更改
    hasUnsavedChanges.value = true;

    ElMessage.success(`已完成 ${audioSegments.length} 个音频片段的切割、上传和保存`);

  } catch (error) {
    console.error('处理音频切割结果失败:', error);
    ElMessage.error('处理音频切割结果失败: ' + error.message);
  }
}

// 切换音频来源的方法
function switchToTTS(audioItem) {
  ElMessageBox.confirm(
    '确定要将此音频改为TTS生成吗？原有的手动音频将被替换。',
    '确认操作',
    { type: 'warning' }
  ).then(() => {
    // 重置为TTS模式，保持说话人信息不变
    audioItem.audioSource = 'tts';
    audioItem.url = '';
    audioItem.duration = 0;
    audioItem.status = 'pending';

    // 标记有未保存的更改
    hasUnsavedChanges.value = true;

    ElMessage.success('已切换为TTS生成模式，请重新生成音频');
  }).catch(() => {
    // 用户取消
  });
}

function switchToManual(audioItem) {
  ElMessageBox.confirm(
    '确定要将此音频改为手动切割吗？需要重新上传和切割音频文件。',
    '确认操作',
    { type: 'warning' }
  ).then(() => {
    // 重置为手动模式，保持说话人信息不变
    audioItem.audioSource = 'manual';
    audioItem.url = '';
    audioItem.duration = 0;
    audioItem.status = 'pending';

    // 标记有未保存的更改
    hasUnsavedChanges.value = true;

    ElMessage.success('已切换为手动切割模式，请重新切割音频');
  }).catch(() => {
    // 用户取消
  });
}

// 监听对话框可见性变化
watch(dialogVisible, (newVal, oldVal) => {
  // 当对话框从显示变为隐藏，且有未保存的更改时
  if (oldVal && !newVal && hasUnsavedChanges.value) {
    // 恢复原始数据
    resetToOriginal();
    console.log('对话框关闭，恢复原始数据');
  }
});

// 计算属性：主要源语言
const primarySourceLanguage = computed(() => {
  const sourceItems = audioItems.value.filter(item =>
    item.audioType === 'source' || item.audioType === 'keyword'
  );

  if (sourceItems.length === 0) return 'ja'; // 默认日语（历史兼容）

  // 统计各语言出现频次
  const languageCount = {};
  sourceItems.forEach(item => {
    const lang = item.language || 'ja';
    languageCount[lang] = (languageCount[lang] || 0) + 1;
  });

  // 返回出现最多的语言
  return Object.keys(languageCount).reduce((a, b) =>
    languageCount[a] > languageCount[b] ? a : b
  );
});

// 计算属性：源语言标签
const sourceLanguageLabel = computed(() => {
  return getLanguageLabel(primarySourceLanguage.value);
});

// 计算属性：是否有源语言内容（用于显示语言级别选择器）
const hasSourceLanguageContent = computed(() => {
  return audioItems.value.some(item =>
    (item.audioType === 'source' || item.audioType === 'keyword') &&
    item.language === primarySourceLanguage.value
  );
});

// 计算属性：源语言内容
const sourceItems = computed(() => {
  return filteredAudioItems.value.filter(item =>
    item.audioType === 'source' || item.audioType === 'keyword'
  );
});

// 计算属性：翻译内容
const translationItems = computed(() => {
  return filteredAudioItems.value.filter(item =>
    item.audioType === 'translation'
  );
});

// 计算属性：是否有源语言内容
const hasSourceContent = computed(() => {
  return sourceItems.value.length > 0;
});

// 计算属性：是否有翻译内容
const hasTranslationContent = computed(() => {
  return translationItems.value.length > 0;
});

// 生成所有源语言音频
async function generateAllSourceAudio() {
  const items = sourceItems.value.filter(item => !item.url);
  for (const item of items) {
    await generateAudio(item);
  }
}

// 生成所有翻译音频
async function generateAllTranslationAudio() {
  const items = translationItems.value.filter(item => !item.url);
  for (const item of items) {
    await generateAudio(item);
  }
}
</script>

<style scoped>
.audio-component {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.preview-section {
  margin-top: 0.75rem;
  padding: 0.625rem;
  background-color: #f8f9fa;
  border-radius: 0.25rem;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
  font-weight: bold;
  color: #606266;
}

.preview-info {
  text-align: center;
  font-size: 0.875rem;
  padding: 0.625rem;
  border-radius: 0.25rem;
  margin-top: 0.75rem;
}

/* 统一的预览背景颜色 */
.success-bg {
  color: #67c23a;
  background-color: #f0f9eb;
}

.warning-bg {
  color: #e6a23c;
  background-color: #fdf6ec;
}

/* 确保音频预览与其他预览保持一致的颜色 */
.audio-preview.success-bg {
  color: #67c23a;
  background-color: #f0f9eb;
}

.audio-preview.warning-bg {
  color: #e6a23c;
  background-color: #fdf6ec;
}

.preview-progress {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.progress-bar {
  height: 0.5rem;
  background-color: #e4e7ed;
  border-radius: 0.25rem;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: #67C23A;
  border-radius: 0.25rem;
  transition: width 0.3s ease;
}

.progress-text {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.75rem;
}

.status-tag {
  padding: 0.125rem 0.375rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
}

.status-tag.success {
  background-color: #f0f9eb;
  color: #67c23a;
  border: 0.0625rem solid #e1f3d8;
}

.status-tag.warning {
  background-color: #fdf6ec;
  color: #e6a23c;
  border: 0.0625rem solid #faecd8;
}

.status-tag.info {
  background-color: #f4f4f5;
  color: #909399;
  border: 0.0625rem solid #e9e9eb;
}

.dialog-header-fixed {
  padding: 20px 20px 0 20px;
  /* 顶部和左右保持间距，底部留空 */
  background: #fff;
  border-bottom: 1px solid #ebeef5;
  z-index: 10;
  position: sticky;
  top: 0;
}

.dialog-toolbar {
  margin-bottom: 15px;
}

.filter-section {
  display: flex;
  align-items: center;
  gap: 10px;
}

.label {
  font-size: 14px;
  color: #606266;
  white-space: nowrap;
}

.speed-settings {
  margin-bottom: 20px;
  padding: 15px;
  background: #f5f7fa;
  border-radius: 4px;
}

.speed-row {
  display: flex;
  align-items: center;
  gap: 10px;
}

.dialog-content-scrollable {
  padding: 20px;
  flex-grow: 1;
  overflow-y: auto;
  /* 允许卡片内容区域滚动 */
}

.audio-cards {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.audio-group {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.group-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.group-header h3 {
  margin: 0;
  font-size: 16px;
  color: #303133;
}

.cards-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 15px;
}

.audio-card {
  background: #fff;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px 15px;
  background-color: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
}

.card-index {
  font-weight: bold;
  color: #606266;
  font-size: 16px;
}

.card-content {
  padding: 15px;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.text-content {
  margin-bottom: 15px;
  flex-grow: 1;
}

.processed-text {
  margin-top: 10px;
  padding-top: 10px;
  border-top: 1px dashed #dcdfe6;
}

.text-label {
  font-size: 12px;
  color: #909399;
  margin-bottom: 5px;
}

.control-panel {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-top: auto;
  /* 将控制面板推到底部 */
}

.status-tags {
  display: flex;
  gap: 5px;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
}

.speed-control {
  display: flex;
  align-items: center;
  gap: 10px;
}

.player-control {
  display: flex;
  align-items: center;
  gap: 10px;
}

.audio-player {
  flex: 1;
}

/* 响应式布局 */
@media (max-width: 768px) {
  .cards-container {
    grid-template-columns: 1fr;
  }
}
</style>
