<!--
  首页侧边栏组件
  用于显示用户信息和设置功能
-->
<template>
  <div class="sidebar-container">
    <!-- 侧边栏头部 -->
    <div class="sidebar-header">
      <img src="@/assets/logo.jpg" alt="Echo Lab" class="sidebar-logo" />
      <h3 class="sidebar-title">Echo Lab</h3>
    </div>

    <!-- 用户信息区域 -->
    <div class="user-section">
      <template v-if="isLoggedIn">
        <div class="user-info" @click="goToProfile" title="查看个人信息">
          <el-avatar :size="48" :src="avatarUrl">
            {{ userInitials }}
          </el-avatar>
          <div class="user-details">
            <h4>{{ displayName }}</h4>
          </div>
        </div>
      </template>
      <template v-else>
        <div class="login-section">
          <el-button type="primary" @click="goToLogin" size="small">
            <el-icon class="el-icon--left">
              <i-ep-user />
            </el-icon>
            登录/注册
          </el-button>
        </div>
      </template>
    </div>

    <!-- 功能菜单 -->
    <div class="menu-section">
      <el-menu class="sidebar-menu" :default-active="activeMenu" @select="handleMenuSelect" background-color="#ffffff"
        text-color="#606266" active-text-color="#409eff">
        <el-menu-item index="/">
          <el-icon>
            <i-ep-home-filled />
          </el-icon>
          <template #title>首页</template>
        </el-menu-item>

        <!-- 内容管理菜单项 - 只有拥有权限的用户才能看到，且只在桌面端显示 -->
        <el-menu-item v-if="isLoggedIn && hasContentCreationPermission && !isMobile" index="content_management">
          <el-icon>
            <i-ep-document />
          </el-icon>
          <template #title>内容管理</template>
        </el-menu-item>

        <!-- 编辑器菜单项 - 只有拥有权限的用户才能看到，且只在桌面端显示 -->
        <el-menu-item v-if="isLoggedIn && hasContentCreationPermission && !isMobile" index="editor">
          <el-icon>
            <i-ep-edit />
          </el-icon>
          <template #title>编辑器</template>
        </el-menu-item>



        <el-menu-item index="/settings">
          <el-icon>
            <i-ep-setting />
          </el-icon>
          <template #title>设置</template>
        </el-menu-item>

        <el-menu-item index="/favorites" v-if="isLoggedIn">
          <el-icon>
            <i-ep-star />
          </el-icon>
          <template #title>我的收藏</template>
        </el-menu-item>

        <el-menu-item index="/templates">
          <el-icon>
            <i-ep-collection />
          </el-icon>
          <template #title>学习模式</template>
        </el-menu-item>

        <el-menu-item index="/level-settings">
          <el-icon>
            <i-ep-user />
          </el-icon>
          <template #title>我的水平</template>
        </el-menu-item>

      </el-menu>

      <!-- 语言切换器 -->
      <div class="language-switcher">
        <div class="switcher-label">
          <el-icon>
            <i-ep-setting />
          </el-icon>
          <span>学习语言</span>
        </div>
        <el-button @click="goToLanguageSelection" class="language-switch-button" size="default">
          <el-icon class="el-icon--left">
            <i-ep-setting />
          </el-icon>
          <span class="current-language">
            <span class="language-flag">{{ getLanguageFlag(currentLearningLanguage) }}</span>
            <span class="language-name">{{ currentLanguageLabel }}</span>
          </span>
          <el-icon class="el-icon--right">
            <i-ep-arrow-right />
          </el-icon>
        </el-button>
      </div>

    </div>

    <!-- 不再需要添加到桌面组件 -->

    <!-- 底部操作区 -->
    <div class="sidebar-footer" v-if="isLoggedIn">
      <el-button link @click="handleLogout" class="logout-button">
        <el-icon class="el-icon--left">
          <i-ep-switch-button />
        </el-icon>
        退出登录
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { computed, ref, onMounted } from "vue";
import { useRouter, useRoute } from "vue-router";
import { useUserStore } from "@/stores/userStore";
import { useLanguageStore } from "@/stores/languageStore";
import { ElMessage, ElMessageBox } from 'element-plus';
// 图标现在通过 unplugin-icons 自动导入，无需手动导入

import { checkFeaturePermission } from "@/services/featurePermissionService";
import { isMobileDevice } from "@/utils/deviceDetector";
import { getLanguageFlag } from "@/config/languageLevels";
import GlobalPlaybackSettings from '@/components/settings/GlobalPlaybackSettings.vue';

// 路由
const router = useRouter();
const route = useRoute();

// 用户状态存储
const userStore = useUserStore();
const languageStore = useLanguageStore();

// 计算属性：是否已登录
const isLoggedIn = computed(() => userStore.isLoggedIn);

// 语言相关状态
const currentLearningLanguage = computed(() => languageStore.currentLearningLanguage);
const currentLanguageLabel = computed(() => languageStore.currentLanguageLabel);

// 计算属性：用户信息
const user = computed(() => userStore.user);

// 检测是否为移动设备
const isMobile = computed(() => isMobileDevice());



// 权限状态
const hasContentCreationPermission = ref(false);

// 在组件挂载时检查权限
onMounted(async () => {
  if (isLoggedIn.value) {
    try {
      hasContentCreationPermission.value = await checkFeaturePermission('content_creation');
    } catch (error) {
      console.error('检查内容创建权限失败:', error);
    }
  }
});

// 计算属性：显示名称
const displayName = computed(() => {
  if (!user.value) return "";
  return user.value.username || user.value.email.split("@")[0];
});

// 计算属性：用户头像URL
const avatarUrl = computed(() => {
  if (!user.value) return "";
  return user.value.avatar || "";
});

// 计算属性：用户名首字母（用于头像显示）
const userInitials = computed(() => {
  if (!user.value) return "";
  if (user.value.username) {
    return user.value.username.substring(0, 1).toUpperCase();
  }
  return user.value.email.substring(0, 1).toUpperCase();
});

// 计算当前激活的菜单项
const activeMenu = computed(() => {
  // 特殊处理内容管理和编辑器页面
  if (route.path === '/content') {
    return 'content_management';
  } else if (route.path.startsWith('/editor')) {
    return 'editor';
  }
  return route.path;
});

// 处理菜单选择
function handleMenuSelect(index) {
  // 特殊处理内容管理和编辑器入口
  if (index === 'content_management') {
    if (hasContentCreationPermission.value) {
      window.open('/content', '_blank');
    } else {
      ElMessage.warning('您没有内容创建与管理权限，请升级会员等级');
      router.push('/upgrade?redirect=' + encodeURIComponent('/content'));
    }
  } else if (index === 'editor') {
    if (hasContentCreationPermission.value) {
      window.open('/editor', '_blank');
    } else {
      ElMessage.warning('您没有内容创建与管理权限，请升级会员等级');
      router.push('/upgrade?redirect=' + encodeURIComponent('/editor'));
    }
  } else if (index === 'global-playback') {
    // 显示全局播放策略设置对话框
    showGlobalPlaybackDialog.value = true;
  } else {
    // 其他菜单项正常处理
    router.push(index);
  }
}

// 跳转到语言选择页面
function goToLanguageSelection() {
  console.log('跳转到语言选择页面');

  // 清除引导完成标记，允许重新进入引导流程
  localStorage.removeItem('echolab_onboarding_completed');
  localStorage.removeItem('echolab_onboarding_completed_at');

  // 跳转到语言选择页面
  router.push('/onboarding/language');
}

// 跳转到登录页面
function goToLogin() {
  router.push("/login");
}

// 跳转到个人信息页面
function goToProfile() {
  router.push("/profile");
}

// 跳转到升级页面 - 在其他地方使用
// function goToUpgrade(redirect) {
//   router.push(`/upgrade?redirect=${encodeURIComponent(redirect)}`);
// }



// 处理退出登录
function handleLogout() {
  ElMessageBox.confirm("确定要退出登录吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      userStore.logout();
      router.push("/");
    })
    .catch(() => {
      // 取消退出登录
    });
}
</script>

<style scoped>
.sidebar-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-color: #ffffff;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

/* 侧边栏头部 */
.sidebar-header {
  height: 4rem;
  display: flex;
  align-items: center;
  padding: 0 1rem;
  background-color: #f5f7fa;
  color: #303133;
  border-bottom: 1px solid #e4e7ed;
}

.sidebar-logo {
  width: 2rem;
  height: 2rem;
  margin-right: 0.75rem;
}

.sidebar-title {
  font-size: 1.125rem;
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-weight: 600;
}

/* 用户信息区域 */
.user-section {
  padding: 1rem;
  border-bottom: 1px solid #e4e7ed;
  background-color: #ffffff;
}

.user-info {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 0.25rem;
  transition: background-color 0.3s;
}

.user-info:hover {
  background-color: #f0f2f5;
}

.user-details {
  margin-left: 0.75rem;
  color: #606266;
}

.user-details h4 {
  margin: 0;
  font-size: 1rem;
  color: #303133;
  font-weight: 500;
}

.login-section {
  display: flex;
  justify-content: center;
}

/* 菜单区域 */
.menu-section {
  flex: 1;
  overflow-y: auto;
  background-color: #ffffff;
}

.sidebar-menu {
  border-right: none;
}

.sidebar-menu :deep(.el-menu-item) {
  height: 3rem;
  line-height: 3rem;
  margin: 0.25rem 0;
  border-radius: 0.25rem;
}

.sidebar-menu :deep(.el-menu-item.is-active) {
  background-color: #ecf5ff;
}

.sidebar-menu :deep(.el-menu-item:hover) {
  background-color: #f5f7fa;
}

/* 统一菜单图标大小 */
.sidebar-menu :deep(.el-menu-item .el-icon) {
  font-size: 1.125rem !important;
  width: 1.125rem;
  height: 1.125rem;
  margin-right: 0.5rem;
}

/* 底部区域 */
.sidebar-footer {
  padding: 1rem;
  border-top: 1px solid #e4e7ed;
  text-align: center;
  background-color: #f5f7fa;
}

.logout-button {
  color: #606266;
}

.logout-button:hover {
  color: #409eff;
}

/* 高级功能菜单项样式 */
.premium-menu-item {
  position: relative;
  margin: 0.25rem 0;
}

.menu-item-container {
  position: relative;
  display: flex;
  align-items: center;
}

.menu-item-container :deep(.el-menu-item) {
  flex-grow: 1;
  opacity: 0.7;
  margin: 0;
}

.upgrade-badge {
  position: absolute;
  right: 0.5rem;
  top: 50%;
  transform: translateY(-50%);
  background-color: #f56c6c;
  color: white;
  padding: 0.125rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-weight: bold;
  cursor: pointer;
  z-index: 10;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.upgrade-badge:hover {
  background-color: #e64242;
  transform: translateY(-50%) scale(1.05);
}

/* 移动端样式 - 使用类名而非媒体查询 */
.mobile-device .sidebar-menu :deep(.el-menu-item) {
  height: 2.75rem;
  line-height: 2.75rem;
}

.mobile-device .upgrade-badge {
  padding: 0.1rem 0.4rem;
  font-size: 0.7rem;
  right: 0.3rem;
}

.mobile-device .menu-item-container :deep(.el-menu-item) {
  padding-right: 3rem;
}

/* 语言切换器样式 */
.language-switcher {
  padding: 1rem;
  border-top: 1px solid #e4e7ed;
  margin-top: 1rem;
}

.switcher-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: #606266;
}

.language-switch-button {
  width: 100%;
  height: auto;
  padding: 0.75rem;
  justify-content: space-between;
  border: 1px solid #e4e7ed;
  background: #ffffff;
}

.language-switch-button:hover {
  border-color: #409eff;
  background: #f0f9ff;
}

.current-language {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex: 1;
  justify-content: center;
}

.language-flag {
  font-size: 1rem;
}

.language-name {
  font-size: 0.875rem;
  font-weight: 500;
}

/* 移动端语言切换器样式 */
.mobile-device .language-switcher {
  padding: 0.75rem;
}

.mobile-device .switcher-label {
  font-size: 0.8rem;
  margin-bottom: 0.5rem;
}

.mobile-device .language-switch-button {
  padding: 0.625rem;
}

.mobile-device .language-name {
  font-size: 0.8rem;
}
</style>
