/**
 * 语言状态管理
 * 管理用户当前学习的语言
 */

import { defineStore } from 'pinia';
import { SUPPORTED_LANGUAGES, getLanguageLabel } from '@/config/languages';

export const useLanguageStore = defineStore('language', {
  state: () => ({
    // 当前学习的语言
    currentLearningLanguage: 'ja', // 默认日语
    
    // 支持的语言列表
    supportedLanguages: SUPPORTED_LANGUAGES,
  }),

  getters: {
    /**
     * 获取当前学习语言的标签
     */
    currentLanguageLabel(state) {
      return getLanguageLabel(state.currentLearningLanguage);
    },

    /**
     * 获取当前学习语言的配置对象
     */
    currentLanguageConfig(state) {
      return state.supportedLanguages.find(
        lang => lang.value === state.currentLearningLanguage
      );
    },

    /**
     * 检查是否已设置学习语言
     */
    hasLanguageSet(state) {
      return !!state.currentLearningLanguage;
    }
  },

  actions: {
    /**
     * 设置当前学习语言
     * @param {string} languageCode 语言代码
     */
    setLearningLanguage(languageCode) {
      if (!languageCode) return;
      
      // 验证语言代码是否支持
      const isSupported = this.supportedLanguages.some(
        lang => lang.value === languageCode
      );
      
      if (!isSupported) {
        console.warn(`不支持的语言代码: ${languageCode}`);
        return;
      }

      this.currentLearningLanguage = languageCode;
      
      // 保存到本地存储
      localStorage.setItem('echolab_learning_language', languageCode);
      
      console.log(`学习语言已切换为: ${this.currentLanguageLabel}`);
    },

    /**
     * 从本地存储加载学习语言
     */
    loadLearningLanguage() {
      try {
        const saved = localStorage.getItem('echolab_learning_language');
        if (saved) {
          // 验证保存的语言是否仍然支持
          const isSupported = this.supportedLanguages.some(
            lang => lang.value === saved
          );
          
          if (isSupported) {
            this.currentLearningLanguage = saved;
            console.log(`已加载学习语言: ${this.currentLanguageLabel}`);
          } else {
            console.warn(`保存的语言代码不再支持: ${saved}，使用默认语言`);
            this.setLearningLanguage('ja'); // 使用默认日语
          }
        }
      } catch (error) {
        console.error('加载学习语言失败:', error);
        this.setLearningLanguage('ja'); // 出错时使用默认日语
      }
    },

    /**
     * 重置为默认语言
     */
    resetToDefault() {
      this.setLearningLanguage('ja');
    },

    /**
     * 切换学习语言（带确认）
     * @param {string} languageCode 新的语言代码
     * @returns {Promise<boolean>} 是否成功切换
     */
    async switchLanguage(languageCode) {
      if (languageCode === this.currentLearningLanguage) {
        return true; // 相同语言，无需切换
      }

      try {
        this.setLearningLanguage(languageCode);
        
        // 可以在这里添加切换后的处理逻辑
        // 比如清除相关缓存、重新加载内容等
        
        return true;
      } catch (error) {
        console.error('切换学习语言失败:', error);
        return false;
      }
    },

    /**
     * 获取语言选项列表（用于UI显示）
     */
    getLanguageOptions() {
      return this.supportedLanguages.map(lang => ({
        value: lang.value,
        label: lang.label,
        isActive: lang.value === this.currentLearningLanguage
      }));
    }
  }
});

export default useLanguageStore;
