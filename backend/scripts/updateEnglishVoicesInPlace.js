/**
 * 原地更新英语声音配置
 * 复用现有记录，只修改角色信息字段
 */

const db = require("../models");

// 英语声音更新映射（旧ID -> 新配置）
const englishVoiceUpdates = [
  {
    oldSpeakerId: "en-US-Wavenet-A",
    newConfig: {
      speaker_id: "en-US-Chirp3-HD-Achernar",
      name: "女声A",
      gender: "female",
      is_premium: true,
      api_params: { languageCode: "en-US" },
    }
  },
  {
    oldSpeakerId: "en-US-Wavenet-B", 
    newConfig: {
      speaker_id: "en-US-Chirp3-HD-Achird",
      name: "男声A",
      gender: "male",
      is_premium: true,
      api_params: { languageCode: "en-US" },
    }
  },
  {
    oldSpeakerId: "en-US-Wavenet-C",
    newConfig: {
      speaker_id: "en-US-Chirp3-HD-Aoede", 
      name: "女声B",
      gender: "female",
      is_premium: true,
      api_params: { languageCode: "en-US" },
    }
  },
  {
    oldSpeakerId: "en-US-Wavenet-D",
    newConfig: {
      speaker_id: "en-US-Chirp3-HD-Algenib",
      name: "男声B", 
      gender: "male",
      is_premium: true,
      api_params: { languageCode: "en-US" },
    }
  },
  {
    oldSpeakerId: "en-US-Neural2-F",
    newConfig: {
      speaker_id: "en-US-Chirp3-HD-Achernar", // 复用女声A
      name: "女声A",
      gender: "female", 
      is_premium: true,
      api_params: { languageCode: "en-US" },
    }
  },
  {
    oldSpeakerId: "en-US-Neural2-J",
    newConfig: {
      speaker_id: "en-US-Chirp3-HD-Achird", // 复用男声A
      name: "男声A",
      gender: "male",
      is_premium: true, 
      api_params: { languageCode: "en-US" },
    }
  }
];

/**
 * 原地更新英语声音配置
 */
async function updateEnglishVoicesInPlace() {
  try {
    console.log("开始原地更新英语声音配置...");

    // 查询现有的英语声音记录
    const existingVoices = await db.Voice.findAll({
      where: { language_code: "en" },
      order: [["speaker_id", "ASC"]]
    });

    console.log(`找到 ${existingVoices.length} 个现有英语声音记录`);

    if (existingVoices.length === 0) {
      console.log("❌ 没有找到英语声音记录，请先运行 initVoices.js");
      return false;
    }

    // 显示现有记录
    console.log("\n📋 现有英语声音记录:");
    existingVoices.forEach((voice, index) => {
      console.log(`  ${index + 1}. ID:${voice.id} ${voice.speaker_id} - ${voice.name} (${voice.gender})`);
    });

    let updatedCount = 0;

    // 逐个更新记录
    for (const update of englishVoiceUpdates) {
      const existingVoice = existingVoices.find(v => v.speaker_id === update.oldSpeakerId);
      
      if (existingVoice) {
        console.log(`\n🔄 更新记录 ID:${existingVoice.id}`);
        console.log(`   旧: ${update.oldSpeakerId} - ${existingVoice.name}`);
        console.log(`   新: ${update.newConfig.speaker_id} - ${update.newConfig.name}`);

        // 更新记录
        await existingVoice.update({
          speaker_id: update.newConfig.speaker_id,
          name: update.newConfig.name,
          gender: update.newConfig.gender,
          is_premium: update.newConfig.is_premium,
          api_params: update.newConfig.api_params
        });

        updatedCount++;
        console.log(`   ✅ 更新成功`);
      } else {
        console.log(`\n⚠️  未找到记录: ${update.oldSpeakerId}`);
      }
    }

    // 验证更新结果
    const updatedVoices = await db.Voice.findAll({
      where: { language_code: "en" },
      order: [["speaker_id", "ASC"]]
    });

    console.log(`\n📊 更新统计: ${updatedCount}/${englishVoiceUpdates.length} 个记录已更新`);
    
    console.log("\n✅ 更新后的英语声音列表:");
    updatedVoices.forEach((voice, index) => {
      console.log(`  ${index + 1}. ID:${voice.id} ${voice.speaker_id} - ${voice.name} (${voice.gender})`);
    });

    console.log("\n🎉 英语声音配置原地更新完成!");
    return true;

  } catch (error) {
    console.error("❌ 更新英语声音配置失败:", error);
    return false;
  }
}

/**
 * 查看当前英语声音配置
 */
async function showEnglishVoices() {
  try {
    const englishVoices = await db.Voice.findAll({
      where: { language_code: "en" },
      order: [["speaker_id", "ASC"]]
    });

    console.log("\n📋 当前英语声音配置:");
    if (englishVoices.length === 0) {
      console.log("  (无记录)");
    } else {
      englishVoices.forEach((voice, index) => {
        console.log(`  ${index + 1}. ID:${voice.id} ${voice.speaker_id} - ${voice.name} (${voice.gender})`);
      });
    }
    
    return englishVoices;
  } catch (error) {
    console.error("查询英语声音配置失败:", error);
    return [];
  }
}

/**
 * 主函数
 */
async function main() {
  const args = process.argv.slice(2);
  const command = args[0];

  try {
    if (command === "show") {
      // 只查看当前英语配置
      await showEnglishVoices();
    } else if (command === "update") {
      // 原地更新英语声音配置
      await updateEnglishVoicesInPlace();
    } else {
      console.log("英语声音配置原地更新工具");
      console.log("=" * 30);
      console.log("");
      console.log("使用方法:");
      console.log("  node updateEnglishVoicesInPlace.js show     # 查看当前英语声音配置");
      console.log("  node updateEnglishVoicesInPlace.js update   # 原地更新英语声音配置");
      console.log("");
      console.log("将要更新的映射:");
      englishVoiceUpdates.forEach((update, index) => {
        console.log(`  ${index + 1}. ${update.oldSpeakerId} -> ${update.newConfig.speaker_id}`);
        console.log(`     ${update.newConfig.name} (${update.newConfig.gender})`);
      });
    }
  } catch (error) {
    console.error("执行失败:", error);
  } finally {
    // 关闭数据库连接
    await db.sequelize.close();
  }
}

// 运行脚本
if (require.main === module) {
  main();
}

module.exports = {
  updateEnglishVoicesInPlace,
  showEnglishVoices,
  englishVoiceUpdates
};
