/**
 * 英语 Google TTS 声音测试工具
 * 测试所有英语声音的可用性
 */

const fs = require("fs");
const path = require("path");
const dotenv = require("dotenv");
const dns = require("dns");
const { generateGoogleTTS } = require("../services/ttsService");

// 强制使用IPv4，避免IPv6连接问题
dns.setDefaultResultOrder("ipv4first");

// 加载环境变量
dotenv.config();

// 英语测试文本
const TEST_TEXT =
  "Hello, this is a test of English text-to-speech. How does this voice sound?";

// 英语声音配置（从initVoices.js中提取）
const ENGLISH_VOICES = [
  {
    id: "en-US-Wavenet-A",
    name: "女声A",
    languageCode: "en-US",
    type: "Wavenet",
  },
  {
    id: "en-US-Wavenet-B",
    name: "男声A",
    languageCode: "en-US",
    type: "Wavenet",
  },
  {
    id: "en-US-Wavenet-C",
    name: "女声B",
    languageCode: "en-US",
    type: "Wavenet",
  },
  {
    id: "en-US-Wavenet-D",
    name: "男声B",
    languageCode: "en-US",
    type: "Wavenet",
  },
  {
    id: "en-US-Neural2-F",
    name: "高级女声",
    languageCode: "en-US",
    type: "Neural2",
  },
  {
    id: "en-US-Neural2-J",
    name: "高级男声",
    languageCode: "en-US",
    type: "Neural2",
  },
];

/**
 * 创建输出目录
 */
function createOutputDir() {
  const outputDir = path.join(__dirname, "../english-voices-test");
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
  }
  return outputDir;
}

/**
 * 生成文件名
 */
function generateFileName(voice, index) {
  const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, "");
  const safeVoiceId = voice.id.replace(/[^a-zA-Z0-9-]/g, "_");
  return `${String(index + 1).padStart(
    2,
    "0"
  )}_${safeVoiceId}_${timestamp}.mp3`;
}

/**
 * 测试单个声音
 */
async function testSingleVoice(voice, index, outputDir) {
  try {
    console.log(
      `\n🎵 测试 ${index + 1}/${ENGLISH_VOICES.length}: ${voice.name}`
    );
    console.log(`   声音ID: ${voice.id}`);
    console.log(`   类型: ${voice.type} | 语言: ${voice.languageCode}`);

    const startTime = Date.now();

    const audioBuffer = await generateGoogleTTS(
      TEST_TEXT,
      1.0, // 标准语速
      voice.name,
      2, // 减少重试次数，快速失败
      1000, // 减少重试延迟
      voice.id,
      { languageCode: voice.languageCode }
    );

    const endTime = Date.now();
    const duration = endTime - startTime;

    // 保存文件
    const fileName = generateFileName(voice, index);
    const filePath = path.join(outputDir, fileName);
    fs.writeFileSync(filePath, audioBuffer);

    console.log(
      `   ✅ 成功! 耗时: ${duration}ms | 文件大小: ${(
        audioBuffer.length / 1024
      ).toFixed(1)} KB`
    );
    console.log(`   📁 文件: ${fileName}`);

    return {
      success: true,
      voice,
      fileName,
      fileSize: audioBuffer.length,
      duration,
      index: index + 1,
    };
  } catch (error) {
    console.log(`   ❌ 失败: ${error.message}`);
    return {
      success: false,
      voice,
      error: error.message,
      index: index + 1,
    };
  }
}

/**
 * 测试所有英语声音
 */
async function testAllEnglishVoices() {
  console.log("🚀 英语声音可用性测试开始\n");
  console.log(`📝 测试文本: "${TEST_TEXT}"`);
  console.log(`🎯 总声音数: ${ENGLISH_VOICES.length}`);
  console.log("=" * 60);

  const outputDir = createOutputDir();
  console.log(`📁 输出目录: ${outputDir}\n`);

  const results = [];

  for (let i = 0; i < ENGLISH_VOICES.length; i++) {
    const result = await testSingleVoice(ENGLISH_VOICES[i], i, outputDir);
    results.push(result);

    // 添加延迟避免API限流
    if (i < ENGLISH_VOICES.length - 1) {
      console.log("   ⏳ 等待 1.5 秒...");
      await new Promise((resolve) => setTimeout(resolve, 1500));
    }
  }

  return results;
}

/**
 * 生成详细报告
 */
function generateDetailedReport(results) {
  console.log("\n" + "=" * 60);
  console.log("📊 详细测试报告");
  console.log("=" * 60);

  const successful = results.filter((r) => r.success);
  const failed = results.filter((r) => !r.success);

  // 基本统计
  console.log(`总测试数: ${results.length}`);
  console.log(`成功: ${successful.length}`);
  console.log(`失败: ${failed.length}`);
  console.log(
    `成功率: ${((successful.length / results.length) * 100).toFixed(1)}%`
  );

  if (successful.length > 0) {
    const totalSize = successful.reduce((sum, r) => sum + r.fileSize, 0);
    const avgSize = totalSize / successful.length;
    const totalDuration = successful.reduce((sum, r) => sum + r.duration, 0);
    const avgDuration = totalDuration / successful.length;

    console.log(`平均文件大小: ${(avgSize / 1024).toFixed(1)} KB`);
    console.log(`平均生成时间: ${avgDuration.toFixed(0)} ms`);
  }

  // 按类型统计
  console.log("\n📈 按声音类型统计:");
  const typeStats = {};
  results.forEach((r) => {
    const type = r.voice.type;
    if (!typeStats[type]) {
      typeStats[type] = { total: 0, success: 0 };
    }
    typeStats[type].total++;
    if (r.success) typeStats[type].success++;
  });

  Object.entries(typeStats).forEach(([type, stats]) => {
    const rate = ((stats.success / stats.total) * 100).toFixed(1);
    console.log(`  ${type}: ${stats.success}/${stats.total} (${rate}%)`);
  });

  // 按语言统计
  console.log("\n🌍 按语言统计:");
  const langStats = {};
  results.forEach((r) => {
    const lang = r.voice.languageCode;
    if (!langStats[lang]) {
      langStats[lang] = { total: 0, success: 0 };
    }
    langStats[lang].total++;
    if (r.success) langStats[lang].success++;
  });

  Object.entries(langStats).forEach(([lang, stats]) => {
    const rate = ((stats.success / stats.total) * 100).toFixed(1);
    console.log(`  ${lang}: ${stats.success}/${stats.total} (${rate}%)`);
  });

  // 可用声音列表
  if (successful.length > 0) {
    console.log("\n✅ 可用声音列表:");
    successful.forEach((r) => {
      console.log(
        `  ${r.index.toString().padStart(2, "0")}. ${r.voice.id} - ${
          r.voice.name
        } (${r.voice.type})`
      );
    });
  }

  // 不可用声音列表
  if (failed.length > 0) {
    console.log("\n❌ 不可用声音列表:");
    failed.forEach((r) => {
      console.log(
        `  ${r.index.toString().padStart(2, "0")}. ${r.voice.id} - ${
          r.voice.name
        } (${r.voice.type})`
      );
      console.log(`      错误: ${r.error}`);
    });
  }

  console.log(
    `\n🎉 测试完成! 音频文件保存在: ${path.join(
      __dirname,
      "../english-voices-test"
    )}`
  );
}

/**
 * 主函数
 */
async function main() {
  try {
    const results = await testAllEnglishVoices();
    generateDetailedReport(results);
  } catch (error) {
    console.error("❌ 测试失败:", error.message);
    process.exit(1);
  }
}

// 运行测试
if (require.main === module) {
  main();
}

module.exports = {
  testAllEnglishVoices,
  testSingleVoice,
  generateDetailedReport,
  ENGLISH_VOICES,
};
