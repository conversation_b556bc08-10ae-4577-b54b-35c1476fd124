/**
 * 快速 Google TTS 测试工具
 * 简单快速地测试 Google TTS 音频生成效果
 */

const fs = require('fs');
const path = require('path');
const dotenv = require('dotenv');
const { generateGoogleTTS } = require('../services/ttsService');

// 加载环境变量
dotenv.config();

// 简单测试配置
const QUICK_TESTS = [
  {
    text: 'こんにちは、世界！今日はいい天気ですね。',
    language: 'ja',
    voiceId: 'ja-JP-Wavenet-A',
    voiceName: '日语女声A',
    languageCode: 'ja-JP',
    speed: 1.0
  },
  {
    text: 'Hello, world! How are you today?',
    language: 'en',
    voiceId: 'en-US-Wavenet-A',
    voiceName: '英语女声A',
    languageCode: 'en-US',
    speed: 1.0
  },
  {
    text: '你好，世界！今天天气很好。',
    language: 'zh-CN',
    voiceId: 'cmn-CN-Wavenet-A',
    voiceName: '中文女声A',
    languageCode: 'cmn-CN',
    speed: 1.0
  },
  {
    text: '你好，世界！今天天氣很好。',
    language: 'zh-TW',
    voiceId: 'cmn-TW-Wavenet-A',
    voiceName: '繁中女声A',
    languageCode: 'cmn-TW',
    speed: 1.0
  }
];

/**
 * 创建输出目录
 */
function createOutputDir() {
  const outputDir = path.join(__dirname, '../quick-test-audio');
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
  }
  return outputDir;
}

/**
 * 生成文件名
 */
function generateFileName(test, index) {
  const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
  return `${index + 1}_${test.language}_${test.voiceName}_${timestamp}.mp3`;
}

/**
 * 测试单个音频
 */
async function testSingleAudio(test, index, outputDir) {
  try {
    console.log(`\n🎵 测试 ${index + 1}: ${test.voiceName}`);
    console.log(`   文本: "${test.text}"`);
    console.log(`   语言: ${test.language} | 语速: ${test.speed}x`);
    
    const startTime = Date.now();
    
    const audioBuffer = await generateGoogleTTS(
      test.text,
      test.speed,
      test.voiceName,
      3, // 重试次数
      2000, // 重试延迟
      test.voiceId,
      { languageCode: test.languageCode }
    );
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    // 保存文件
    const fileName = generateFileName(test, index);
    const filePath = path.join(outputDir, fileName);
    fs.writeFileSync(filePath, audioBuffer);
    
    console.log(`   ✅ 成功! 耗时: ${duration}ms | 文件大小: ${(audioBuffer.length / 1024).toFixed(1)} KB`);
    console.log(`   📁 文件: ${fileName}`);
    
    return {
      success: true,
      test,
      fileName,
      fileSize: audioBuffer.length,
      duration
    };
    
  } catch (error) {
    console.log(`   ❌ 失败: ${error.message}`);
    return {
      success: false,
      test,
      error: error.message
    };
  }
}

/**
 * 运行快速测试
 */
async function runQuickTest() {
  console.log('🚀 Google TTS 快速测试开始\n');
  console.log('=' * 50);
  
  const outputDir = createOutputDir();
  console.log(`📁 输出目录: ${outputDir}\n`);
  
  const results = [];
  
  for (let i = 0; i < QUICK_TESTS.length; i++) {
    const result = await testSingleAudio(QUICK_TESTS[i], i, outputDir);
    results.push(result);
    
    // 添加延迟避免API限流
    if (i < QUICK_TESTS.length - 1) {
      console.log('   ⏳ 等待 2 秒...');
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  }
  
  // 生成报告
  console.log('\n' + '=' * 50);
  console.log('📊 测试报告');
  console.log('=' * 50);
  
  const successful = results.filter(r => r.success);
  const failed = results.filter(r => !r.success);
  
  console.log(`总测试数: ${results.length}`);
  console.log(`成功: ${successful.length}`);
  console.log(`失败: ${failed.length}`);
  console.log(`成功率: ${((successful.length / results.length) * 100).toFixed(1)}%`);
  
  if (successful.length > 0) {
    const totalSize = successful.reduce((sum, r) => sum + r.fileSize, 0);
    const avgSize = totalSize / successful.length;
    const totalDuration = successful.reduce((sum, r) => sum + r.duration, 0);
    const avgDuration = totalDuration / successful.length;
    
    console.log(`平均文件大小: ${(avgSize / 1024).toFixed(1)} KB`);
    console.log(`平均生成时间: ${avgDuration.toFixed(0)} ms`);
  }
  
  if (failed.length > 0) {
    console.log('\n❌ 失败的测试:');
    failed.forEach(f => {
      console.log(`  - ${f.test.voiceName}: ${f.error}`);
    });
  }
  
  console.log(`\n🎉 测试完成! 音频文件保存在: ${outputDir}`);
  
  return results;
}

/**
 * 自定义测试
 */
async function customTest(text, language, voiceId, voiceName, languageCode, speed = 1.0) {
  console.log('🎯 自定义测试');
  console.log('=' * 30);
  
  const outputDir = createOutputDir();
  
  const test = {
    text,
    language,
    voiceId,
    voiceName,
    languageCode,
    speed
  };
  
  const result = await testSingleAudio(test, 0, outputDir);
  
  if (result.success) {
    console.log('\n🎉 自定义测试成功!');
  } else {
    console.log('\n❌ 自定义测试失败!');
  }
  
  return result;
}

/**
 * 显示帮助信息
 */
function showHelp() {
  console.log('Google TTS 快速测试工具');
  console.log('=' * 30);
  console.log('');
  console.log('使用方法:');
  console.log('  node quickTTSTest.js                           # 运行快速测试');
  console.log('  node quickTTSTest.js custom <参数>             # 自定义测试');
  console.log('');
  console.log('自定义测试参数:');
  console.log('  <text> <language> <voiceId> <voiceName> <languageCode> [speed]');
  console.log('');
  console.log('示例:');
  console.log('  node quickTTSTest.js custom "Hello world" en en-US-Wavenet-A "英语女声" en-US 1.2');
  console.log('');
  console.log('常用声音ID:');
  console.log('  日语: ja-JP-Wavenet-A, ja-JP-Wavenet-B, ja-JP-Neural2-B');
  console.log('  英语: en-US-Wavenet-A, en-US-Wavenet-B, en-US-Neural2-F');
  console.log('  中文: cmn-CN-Wavenet-A, cmn-CN-Wavenet-B');
  console.log('  繁中: cmn-TW-Wavenet-A, cmn-TW-Wavenet-B');
}

/**
 * 主函数
 */
async function main() {
  const args = process.argv.slice(2);
  
  try {
    if (args.length === 0) {
      // 运行快速测试
      await runQuickTest();
    } else if (args[0] === 'custom' && args.length >= 6) {
      // 自定义测试
      const [, text, language, voiceId, voiceName, languageCode, speed] = args;
      await customTest(text, language, voiceId, voiceName, languageCode, parseFloat(speed) || 1.0);
    } else {
      // 显示帮助
      showHelp();
    }
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    console.error('详细错误:', error);
    process.exit(1);
  }
}

// 运行测试
if (require.main === module) {
  main();
}

module.exports = {
  runQuickTest,
  customTest,
  testSingleAudio
};
