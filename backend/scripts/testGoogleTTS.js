/**
 * Google TTS 音频生成效果测试工具
 * 用于测试不同语言、声音、语速的音频生成效果
 */

const fs = require("fs");
const path = require("path");
const dotenv = require("dotenv");
const dns = require("dns");
const { generateGoogleTTS } = require("../services/ttsService");

// 强制使用IPv4，避免IPv6连接问题
dns.setDefaultResultOrder("ipv4first");

// 加载环境变量
dotenv.config();

// 测试配置
const TEST_CONFIG = {
  // 输出目录
  outputDir: path.join(__dirname, "../test-audio-output"),

  // 测试文本
  testTexts: {
    ja: [
      "こんにちは、世界！今日はいい天気ですね。",
      "プログラミングは楽しいです。一緒に学びましょう。",
      "日本語の音声合成テストです。",
    ],
    en: [
      "Hello, world! How are you today?",
      "Programming is fun. Let's learn together.",
      "This is an English text-to-speech test.",
    ],
    "zh-CN": [
      "你好，世界！今天天气很好。",
      "编程很有趣，让我们一起学习吧。",
      "这是中文语音合成测试。",
    ],
    "zh-TW": [
      "你好，世界！今天天氣很好。",
      "程式設計很有趣，讓我們一起學習吧。",
      "這是繁體中文語音合成測試。",
    ],
  },

  // 测试语速
  testSpeeds: [0.8, 1.0, 1.2],

  // 测试声音配置
  testVoices: {
    ja: [
      { id: "ja-JP-Wavenet-A", name: "女声A", languageCode: "ja-JP" },
      { id: "ja-JP-Wavenet-B", name: "男声A", languageCode: "ja-JP" },
      { id: "ja-JP-Neural2-B", name: "高级男声", languageCode: "ja-JP" },
      { id: "ja-JP-Neural2-C", name: "高级女声", languageCode: "ja-JP" },
    ],
    en: [
      { id: "en-US-Wavenet-A", name: "女声A", languageCode: "en-US" },
      { id: "en-US-Wavenet-B", name: "男声A", languageCode: "en-US" },
      { id: "en-US-Neural2-F", name: "高级女声", languageCode: "en-US" },
      { id: "en-US-Neural2-J", name: "高级男声", languageCode: "en-US" },
    ],
    "zh-CN": [
      { id: "cmn-CN-Wavenet-A", name: "女声A", languageCode: "cmn-CN" },
      { id: "cmn-CN-Wavenet-B", name: "男声A", languageCode: "cmn-CN" },
      { id: "cmn-CN-Wavenet-D", name: "女声B", languageCode: "cmn-CN" },
    ],
    "zh-TW": [
      { id: "cmn-TW-Wavenet-A", name: "女声A", languageCode: "cmn-TW" },
      { id: "cmn-TW-Wavenet-B", name: "男声A", languageCode: "cmn-TW" },
      { id: "cmn-TW-Wavenet-C", name: "男声B", languageCode: "cmn-TW" },
    ],
  },
};

/**
 * 创建输出目录
 */
function createOutputDir() {
  if (!fs.existsSync(TEST_CONFIG.outputDir)) {
    fs.mkdirSync(TEST_CONFIG.outputDir, { recursive: true });
    console.log(`✅ 创建输出目录: ${TEST_CONFIG.outputDir}`);
  }
}

/**
 * 生成安全的文件名
 */
function generateSafeFileName(text, language, voiceName, speed) {
  const safeText = text
    .substring(0, 20)
    .replace(/[^\w\s-]/g, "")
    .replace(/\s+/g, "_");
  const timestamp = Date.now();
  return `${language}_${voiceName}_${speed}x_${safeText}_${timestamp}.mp3`;
}

/**
 * 测试单个音频生成
 */
async function testSingleAudio(text, language, voice, speed) {
  try {
    console.log(
      `🎵 测试: ${language} | ${voice.name} | ${speed}x | "${text.substring(
        0,
        30
      )}..."`
    );

    const audioBuffer = await generateGoogleTTS(
      text,
      speed,
      voice.name,
      3, // 重试次数
      2000, // 重试延迟
      voice.id,
      { languageCode: voice.languageCode }
    );

    // 生成文件名
    const fileName = generateSafeFileName(text, language, voice.name, speed);
    const filePath = path.join(TEST_CONFIG.outputDir, fileName);

    // 保存音频文件
    fs.writeFileSync(filePath, audioBuffer);

    console.log(
      `  ✅ 成功生成: ${fileName} (${(audioBuffer.length / 1024).toFixed(
        1
      )} KB)`
    );

    return {
      success: true,
      fileName,
      fileSize: audioBuffer.length,
      text,
      language,
      voice: voice.name,
      speed,
    };
  } catch (error) {
    console.log(`  ❌ 生成失败: ${error.message}`);
    return {
      success: false,
      error: error.message,
      text,
      language,
      voice: voice.name,
      speed,
    };
  }
}

/**
 * 测试指定语言的所有组合
 */
async function testLanguage(language) {
  console.log(`\n🌍 开始测试语言: ${language}`);

  const texts = TEST_CONFIG.testTexts[language];
  const voices = TEST_CONFIG.testVoices[language];
  const speeds = TEST_CONFIG.testSpeeds;

  if (!texts || !voices) {
    console.log(`❌ 未找到语言 ${language} 的测试配置`);
    return [];
  }

  const results = [];

  // 测试每个文本的第一个声音和第一个语速（快速测试）
  for (const text of texts) {
    const result = await testSingleAudio(text, language, voices[0], speeds[0]);
    results.push(result);

    // 添加延迟避免API限流
    await new Promise((resolve) => setTimeout(resolve, 1000));
  }

  return results;
}

/**
 * 测试所有语言
 */
async function testAllLanguages() {
  console.log("🚀 开始 Google TTS 音频生成效果测试\n");

  createOutputDir();

  const allResults = [];
  const languages = Object.keys(TEST_CONFIG.testTexts);

  for (const language of languages) {
    const results = await testLanguage(language);
    allResults.push(...results);
  }

  return allResults;
}

/**
 * 详细测试指定语言的所有组合
 */
async function testLanguageDetailed(language) {
  console.log(`\n🔍 详细测试语言: ${language}`);

  const texts = TEST_CONFIG.testTexts[language];
  const voices = TEST_CONFIG.testVoices[language];
  const speeds = TEST_CONFIG.testSpeeds;

  if (!texts || !voices) {
    console.log(`❌ 未找到语言 ${language} 的测试配置`);
    return [];
  }

  const results = [];

  // 测试所有组合
  for (const text of texts.slice(0, 1)) {
    // 只测试第一个文本
    for (const voice of voices) {
      for (const speed of speeds) {
        const result = await testSingleAudio(text, language, voice, speed);
        results.push(result);

        // 添加延迟避免API限流
        await new Promise((resolve) => setTimeout(resolve, 1500));
      }
    }
  }

  return results;
}

/**
 * 生成测试报告
 */
function generateReport(results) {
  console.log("\n📊 测试报告");
  console.log("=" * 50);

  const successful = results.filter((r) => r.success);
  const failed = results.filter((r) => !r.success);

  console.log(`总测试数: ${results.length}`);
  console.log(`成功: ${successful.length}`);
  console.log(`失败: ${failed.length}`);
  console.log(
    `成功率: ${((successful.length / results.length) * 100).toFixed(1)}%`
  );

  if (successful.length > 0) {
    const totalSize = successful.reduce((sum, r) => sum + r.fileSize, 0);
    const avgSize = totalSize / successful.length;
    console.log(`平均文件大小: ${(avgSize / 1024).toFixed(1)} KB`);
  }

  if (failed.length > 0) {
    console.log("\n❌ 失败的测试:");
    failed.forEach((f) => {
      console.log(`  - ${f.language} | ${f.voice} | ${f.speed}x: ${f.error}`);
    });
  }

  console.log(`\n📁 音频文件保存在: ${TEST_CONFIG.outputDir}`);
}

/**
 * 主函数
 */
async function main() {
  const args = process.argv.slice(2);
  const command = args[0];
  const language = args[1];

  try {
    let results = [];

    if (command === "quick") {
      // 快速测试所有语言
      results = await testAllLanguages();
    } else if (command === "detailed" && language) {
      // 详细测试指定语言
      results = await testLanguageDetailed(language);
    } else if (command === "single" && language) {
      // 测试单个语言
      results = await testLanguage(language);
    } else {
      console.log("使用方法:");
      console.log(
        "  node testGoogleTTS.js quick                    # 快速测试所有语言"
      );
      console.log(
        "  node testGoogleTTS.js single <language>       # 测试指定语言"
      );
      console.log(
        "  node testGoogleTTS.js detailed <language>     # 详细测试指定语言"
      );
      console.log("");
      console.log("支持的语言: ja, en, zh-CN, zh-TW");
      return;
    }

    generateReport(results);
  } catch (error) {
    console.error("❌ 测试失败:", error.message);
    process.exit(1);
  }
}

// 运行测试
if (require.main === module) {
  main();
}

module.exports = {
  testSingleAudio,
  testLanguage,
  testLanguageDetailed,
  testAllLanguages,
  generateReport,
};
