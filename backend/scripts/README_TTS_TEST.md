# Google TTS 音频生成测试工具

这里提供了两个测试工具来测试 Google TTS 音频生成效果。

## 🚀 快速测试工具 (quickTTSTest.js)

### 功能特点
- 快速测试多种语言的音频生成效果
- 预设了常用的测试文本和声音
- 自动保存生成的音频文件
- 提供详细的测试报告

### 使用方法

#### 1. 快速测试（推荐）
```bash
cd backend
node scripts/quickTTSTest.js
```

这将测试以下语言和声音：
- 日语：女声A
- 英语：女声A  
- 简体中文：女声A
- 繁体中文：女声A

#### 2. 自定义测试
```bash
node scripts/quickTTSTest.js custom "测试文本" 语言 声音ID 声音名称 语言代码 [语速]
```

示例：
```bash
# 测试日语男声
node scripts/quickTTSTest.js custom "こんにちは" ja ja-JP-Wavenet-B "日语男声" ja-JP 1.0

# 测试英语高级女声，语速1.2倍
node scripts/quickTTSTest.js custom "Hello world" en en-US-Neural2-F "英语高级女声" en-US 1.2
```

## 🔍 详细测试工具 (testGoogleTTS.js)

### 功能特点
- 支持批量测试多种组合
- 可测试不同语速和声音
- 更详细的测试选项

### 使用方法

#### 1. 快速测试所有语言
```bash
node scripts/testGoogleTTS.js quick
```

#### 2. 测试指定语言
```bash
node scripts/testGoogleTTS.js single ja     # 测试日语
node scripts/testGoogleTTS.js single en     # 测试英语
node scripts/testGoogleTTS.js single zh-CN  # 测试简体中文
node scripts/testGoogleTTS.js single zh-TW  # 测试繁体中文
```

#### 3. 详细测试指定语言（所有声音和语速组合）
```bash
node scripts/testGoogleTTS.js detailed ja
```

## 📁 输出文件

### 快速测试工具
- 输出目录：`backend/quick-test-audio/`
- 文件命名：`序号_语言_声音名称_时间戳.mp3`

### 详细测试工具  
- 输出目录：`backend/test-audio-output/`
- 文件命名：`语言_声音_语速_文本片段_时间戳.mp3`

## 🎯 常用声音ID参考

### 日语 (ja-JP)
- `ja-JP-Wavenet-A` - 女声A
- `ja-JP-Wavenet-B` - 男声A
- `ja-JP-Wavenet-C` - 男声B
- `ja-JP-Wavenet-D` - 女声B
- `ja-JP-Neural2-B` - 高级男声
- `ja-JP-Neural2-C` - 高级女声

### 英语 (en-US)
- `en-US-Wavenet-A` - 女声A
- `en-US-Wavenet-B` - 男声A
- `en-US-Wavenet-C` - 女声B
- `en-US-Wavenet-D` - 男声B
- `en-US-Neural2-F` - 高级女声
- `en-US-Neural2-J` - 高级男声

### 简体中文 (cmn-CN)
- `cmn-CN-Wavenet-A` - 女声A
- `cmn-CN-Wavenet-B` - 男声A
- `cmn-CN-Wavenet-C` - 男声B
- `cmn-CN-Wavenet-D` - 女声B

### 繁体中文 (cmn-TW)
- `cmn-TW-Wavenet-A` - 女声A
- `cmn-TW-Wavenet-B` - 男声A
- `cmn-TW-Wavenet-C` - 男声B

## ⚙️ 环境要求

### 1. 环境变量
确保 `.env` 文件中配置了 Google API Key：
```
GOOGLE_API_KEY=your_google_api_key_here
```

### 2. 依赖包
确保已安装必要的依赖：
```bash
npm install axios dotenv
```

## 📊 测试报告示例

```
📊 测试报告
==================================================
总测试数: 4
成功: 4
失败: 0
成功率: 100.0%
平均文件大小: 15.2 KB
平均生成时间: 1250 ms

🎉 测试完成! 音频文件保存在: /path/to/backend/quick-test-audio
```

## 🔧 故障排除

### 1. API Key 错误
```
❌ 失败: Request failed with status code 403
```
解决：检查 Google API Key 是否正确配置

### 2. 网络连接问题
```
❌ 失败: connect ECONNREFUSED
```
解决：检查网络连接，确保可以访问 Google API

### 3. 语音ID 不存在
```
❌ 失败: Invalid voice name
```
解决：检查声音ID是否正确，参考上面的常用声音ID列表

## 💡 使用建议

1. **首次使用**：建议先运行快速测试，确保基本功能正常
2. **测试新声音**：使用自定义测试功能测试特定的声音效果
3. **批量测试**：使用详细测试工具进行全面的声音质量评估
4. **API限流**：工具已内置延迟机制，避免触发API限流

## 📝 注意事项

- 测试会消耗 Google TTS API 配额
- 生成的音频文件会保存在本地，注意磁盘空间
- 建议在测试前备份重要数据
- 高级声音（Neural2）可能产生额外费用
