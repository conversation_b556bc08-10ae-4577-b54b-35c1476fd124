/**
 * 新英语 Chirp3-HD 声音测试工具
 */

const fs = require('fs');
const path = require('path');
const dotenv = require('dotenv');
const dns = require('dns');
const { generateGoogleTTS } = require('../services/ttsService');

// 强制使用IPv4，避免IPv6连接问题
dns.setDefaultResultOrder('ipv4first');

// 加载环境变量
dotenv.config();

// 英语测试文本
const TEST_TEXT = "Hello, this is a test of English text-to-speech. How does this voice sound?";

// 新的英语声音配置
const NEW_ENGLISH_VOICES = [
  {
    id: "en-US-Chirp3-HD-Achird",
    name: "男声A",
    languageCode: "en-US",
    type: "Chirp3-HD",
  },
  {
    id: "en-US-Chirp3-HD-Achernar",
    name: "女声A",
    languageCode: "en-US",
    type: "Chirp3-HD",
  },
  {
    id: "en-US-Chirp3-HD-Algenib",
    name: "男声B",
    languageCode: "en-US",
    type: "Chirp3-HD",
  },
  {
    id: "en-US-Chirp3-HD-Aoede",
    name: "女声B",
    languageCode: "en-US",
    type: "Chirp3-HD",
  },
];

/**
 * 创建输出目录
 */
function createOutputDir() {
  const outputDir = path.join(__dirname, '../chirp3-hd-test');
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
  }
  return outputDir;
}

/**
 * 生成文件名
 */
function generateFileName(voice, index) {
  const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
  const safeVoiceId = voice.id.replace(/[^a-zA-Z0-9-]/g, '_');
  return `${String(index + 1).padStart(2, '0')}_${safeVoiceId}_${timestamp}.mp3`;
}

/**
 * 测试单个声音
 */
async function testSingleVoice(voice, index, outputDir) {
  try {
    console.log(`\n🎵 测试 ${index + 1}/${NEW_ENGLISH_VOICES.length}: ${voice.name}`);
    console.log(`   声音ID: ${voice.id}`);
    console.log(`   类型: ${voice.type} | 语言: ${voice.languageCode}`);
    
    const startTime = Date.now();
    
    const audioBuffer = await generateGoogleTTS(
      TEST_TEXT,
      1.0, // 标准语速
      voice.name,
      2, // 减少重试次数，快速失败
      1000, // 减少重试延迟
      voice.id,
      { languageCode: voice.languageCode }
    );
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    // 保存文件
    const fileName = generateFileName(voice, index);
    const filePath = path.join(outputDir, fileName);
    fs.writeFileSync(filePath, audioBuffer);
    
    console.log(`   ✅ 成功! 耗时: ${duration}ms | 文件大小: ${(audioBuffer.length / 1024).toFixed(1)} KB`);
    console.log(`   📁 文件: ${fileName}`);
    
    return {
      success: true,
      voice,
      fileName,
      fileSize: audioBuffer.length,
      duration,
      index: index + 1
    };
    
  } catch (error) {
    console.log(`   ❌ 失败: ${error.message}`);
    return {
      success: false,
      voice,
      error: error.message,
      index: index + 1
    };
  }
}

/**
 * 测试所有新英语声音
 */
async function testAllNewEnglishVoices() {
  console.log('🚀 新英语 Chirp3-HD 声音测试开始\n');
  console.log(`📝 测试文本: "${TEST_TEXT}"`);
  console.log(`🎯 总声音数: ${NEW_ENGLISH_VOICES.length}`);
  console.log('=' * 60);
  
  const outputDir = createOutputDir();
  console.log(`📁 输出目录: ${outputDir}\n`);
  
  const results = [];
  
  for (let i = 0; i < NEW_ENGLISH_VOICES.length; i++) {
    const result = await testSingleVoice(NEW_ENGLISH_VOICES[i], i, outputDir);
    results.push(result);
    
    // 添加延迟避免API限流
    if (i < NEW_ENGLISH_VOICES.length - 1) {
      console.log('   ⏳ 等待 2 秒...');
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  }
  
  return results;
}

/**
 * 生成测试报告
 */
function generateReport(results) {
  console.log('\n' + '=' * 60);
  console.log('📊 测试报告');
  console.log('=' * 60);
  
  const successful = results.filter(r => r.success);
  const failed = results.filter(r => !r.success);
  
  console.log(`总测试数: ${results.length}`);
  console.log(`成功: ${successful.length}`);
  console.log(`失败: ${failed.length}`);
  console.log(`成功率: ${((successful.length / results.length) * 100).toFixed(1)}%`);
  
  if (successful.length > 0) {
    const totalSize = successful.reduce((sum, r) => sum + r.fileSize, 0);
    const avgSize = totalSize / successful.length;
    const totalDuration = successful.reduce((sum, r) => sum + r.duration, 0);
    const avgDuration = totalDuration / successful.length;
    
    console.log(`平均文件大小: ${(avgSize / 1024).toFixed(1)} KB`);
    console.log(`平均生成时间: ${avgDuration.toFixed(0)} ms`);
  }
  
  // 可用声音列表
  if (successful.length > 0) {
    console.log('\n✅ 可用声音列表:');
    successful.forEach(r => {
      console.log(`  ${r.index}. ${r.voice.id} - ${r.voice.name}`);
    });
  }
  
  // 不可用声音列表
  if (failed.length > 0) {
    console.log('\n❌ 不可用声音列表:');
    failed.forEach(r => {
      console.log(`  ${r.index}. ${r.voice.id} - ${r.voice.name}`);
      console.log(`      错误: ${r.error}`);
    });
  }
  
  console.log(`\n🎉 测试完成! 音频文件保存在: ${path.join(__dirname, '../chirp3-hd-test')}`);
}

/**
 * 主函数
 */
async function main() {
  try {
    const results = await testAllNewEnglishVoices();
    generateReport(results);
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    process.exit(1);
  }
}

// 运行测试
if (require.main === module) {
  main();
}

module.exports = {
  testAllNewEnglishVoices,
  testSingleVoice,
  generateReport,
  NEW_ENGLISH_VOICES
};
